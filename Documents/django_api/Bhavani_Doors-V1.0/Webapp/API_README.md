# Bhavani Doors REST API

## Overview

This REST API provides dynamic CRUD operations for all models registered in the Django Admin panel of the Bhavani Doors application. The API automatically discovers models and exposes them through standardized endpoints while maintaining the same authentication and permission system as the Django Admin.

## Key Features

### 🔄 Dynamic Model Discovery
- Automatically exposes all models registered in Django Admin
- No code changes required when new models are added
- Automatic field and relationship detection

### 🔐 Secure Authentication
- Uses the same session-based authentication as Django Admin
- CSRF protection enabled
- Maintains existing permission levels

### 📊 Rich Metadata
- Provides comprehensive model schema information
- Field types, validation rules, and constraints
- Relationship information for form generation
- Admin configuration details

### 🎯 Angular-Ready
- Response formats designed for dynamic UI generation
- Includes field metadata for automatic form creation
- CORS configured for Angular development

## Installation & Setup

### 1. Install Dependencies
```bash
pip install djangorestframework django-cors-headers
```

### 2. Configuration
The API is already configured in `settings.py` with:
- Django REST Framework
- CORS headers for Angular
- Session authentication
- Proper middleware setup

### 3. URL Configuration
API endpoints are available under `/api/` prefix.

## API Endpoints

### Global Endpoints

#### Health Check
```
GET /api/health/
```
Returns API status and authentication information.

#### List All Models
```
GET /api/models/
```
Returns all available models with metadata.

### Model-Specific Endpoints

For each model `{app_label}.{model_name}`, the following endpoints are available:

#### Schema Information
```
GET /api/models/{app_label}/{model_name}/schema/
```
Returns complete model schema including fields, relationships, and admin configuration.

#### CRUD Operations
```
GET    /api/models/{app_label}/{model_name}/           # List instances
POST   /api/models/{app_label}/{model_name}/           # Create instance
GET    /api/models/{app_label}/{model_name}/{id}/      # Get instance
PUT    /api/models/{app_label}/{model_name}/{id}/      # Update instance
DELETE /api/models/{app_label}/{model_name}/{id}/      # Delete instance
```

#### Utility Endpoints
```
GET /api/models/{app_label}/{model_name}/stats/                           # Model statistics
GET /api/models/{app_label}/{model_name}/fields/{field_name}/choices/     # Related field choices
GET /api/models/{app_label}/{model_name}/fields/{field_name}/options/     # Field choice options
POST /api/models/{app_label}/{model_name}/validate/                       # Validate data
```

## Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "errors": [ ... ],
  "metadata": { ... },
  "pagination": { ... }
}
```

### Success Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Sample Material",
    "design": 2,
    "available_quantity": 100.50
  },
  "message": "Material retrieved successfully"
}
```

### Error Response
```json
{
  "success": false,
  "errors": {
    "name": ["This field is required."],
    "unit_price": ["Ensure this value is greater than 0."]
  },
  "message": "Validation failed"
}
```

### List Response with Pagination
```json
{
  "success": true,
  "data": [ ... ],
  "pagination": {
    "count": 150,
    "next": "/api/models/bhavani_doors/material/?page=2",
    "previous": null,
    "page_size": 20
  },
  "metadata": {
    "model_name": "material",
    "verbose_name": "Material",
    "fields": { ... }
  }
}
```

## Available Models

The API automatically exposes these models from the Bhavani Doors system:

### Core Models
- `bhavani_doors.material` - Materials/Products
- `bhavani_doors.design` - Product designs
- `bhavani_doors.unit` - Measurement units
- `bhavani_doors.material_type` - Material categories

### Business Entities
- `bhavani_doors.supplier` - Suppliers
- `bhavani_doors.customer` - Customers
- `bhavani_doors.customer_type` - Customer categories

### Inventory Management
- `bhavani_doors.stock_order` - Stock orders
- `bhavani_doors.stock_order_item` - Stock order items
- `bhavani_doors.supplier_payment` - Supplier payments
- `bhavani_doors.stock_return` - Stock returns

### Sales Management
- `bhavani_doors.order` - Customer orders
- `bhavani_doors.order_item` - Order items
- `bhavani_doors.customer_payment` - Customer payments
- `bhavani_doors.order_return` - Order returns

### GST/Tax Management
- `bhavani_doors.gst_order` - GST orders
- `bhavani_doors.gst_order_item` - GST order items
- `bhavani_doors.gst_material` - GST materials
- `bhavani_doors.gst_tax` - GST tax configuration

## Authentication

The API uses Django's session-based authentication, the same as the Admin panel.

### Login Process
1. User must first login through Django Admin (`/admin/`)
2. Session cookie is automatically included in subsequent API requests
3. CSRF token is required for POST/PUT/DELETE operations

### CSRF Protection
For write operations, include the CSRF token:
```javascript
// Get CSRF token from cookie or meta tag
const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

// Include in request headers
headers: {
  'X-CSRFToken': csrfToken,
  'Content-Type': 'application/json'
}
```

## Usage Examples

### Get All Models
```javascript
fetch('/api/models/')
  .then(response => response.json())
  .then(data => console.log(data));
```

### Get Model Schema
```javascript
fetch('/api/models/bhavani_doors/material/schema/')
  .then(response => response.json())
  .then(schema => {
    // Use schema to generate forms dynamically
    console.log(schema.data.fields);
  });
```

### Create New Material
```javascript
const materialData = {
  name: "New Door",
  design: 1,
  unit: 1,
  unit_order_price: 150.00,
  sales_type: "single"
};

fetch('/api/models/bhavani_doors/material/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRFToken': csrfToken
  },
  body: JSON.stringify(materialData)
})
.then(response => response.json())
.then(result => console.log(result));
```

## Development Tools

### Generate API Documentation
```bash
python manage.py generate_api_docs --format markdown --output api_docs.md
python manage.py generate_api_docs --format json --output api_docs.json
python manage.py generate_api_docs --format html --output api_docs.html
```

### Test API Health
```bash
curl -X GET http://localhost:8000/api/health/
```

## Angular Integration

The API is designed to work seamlessly with Angular applications:

1. **Dynamic Form Generation**: Use schema endpoints to generate forms
2. **Automatic Validation**: Field validation rules are included in responses
3. **Relationship Handling**: Related field choices are available through dedicated endpoints
4. **CORS Support**: Configured for Angular development server

### Example Angular Service
```typescript
@Injectable()
export class ApiService {
  constructor(private http: HttpClient) {}
  
  getModels() {
    return this.http.get<ApiResponse>('/api/models/');
  }
  
  getModelSchema(appLabel: string, modelName: string) {
    return this.http.get<ApiResponse>(`/api/models/${appLabel}/${modelName}/schema/`);
  }
  
  getModelData(appLabel: string, modelName: string, params?: any) {
    return this.http.get<ApiResponse>(`/api/models/${appLabel}/${modelName}/`, { params });
  }
}
```

## Security Considerations

- ✅ Session-based authentication (same as Django Admin)
- ✅ CSRF protection enabled
- ✅ Permission checks maintained
- ✅ Input validation and sanitization
- ✅ Rate limiting ready (can be added)
- ✅ CORS properly configured

## Future Enhancements

- [ ] Rate limiting implementation
- [ ] API versioning
- [ ] Bulk operations support
- [ ] Advanced filtering and sorting
- [ ] Export/import functionality
- [ ] Real-time updates via WebSockets
