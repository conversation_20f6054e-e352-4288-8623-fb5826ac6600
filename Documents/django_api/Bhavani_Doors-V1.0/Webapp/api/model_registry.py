"""
Dynamic Model Registry for API
Automatically discovers and manages models registered in Django Admin
"""
import inspect
from django.apps import apps
from django.contrib import admin
from django.db import models
from django.contrib.admin.options import ModelAdmin
from collections import OrderedDict


class ModelRegistry:
    """
    Registry that automatically discovers models from Django Admin
    and provides metadata for dynamic API generation
    """
    
    def __init__(self):
        self._models = {}
        self._admin_classes = {}
        self._discover_models()
    
    def _discover_models(self):
        """Discover all models registered in Django Admin"""
        # Get all registered admin models
        for model, admin_class in admin.site._registry.items():
            app_label = model._meta.app_label
            model_name = model._meta.model_name
            
            # Skip Django's built-in models
            if app_label in ['auth', 'admin', 'contenttypes', 'sessions']:
                continue
                
            self._models[f"{app_label}.{model_name}"] = model
            self._admin_classes[f"{app_label}.{model_name}"] = admin_class
    
    def get_all_models(self):
        """Get all discovered models"""
        return self._models
    
    def get_model(self, app_label, model_name):
        """Get a specific model by app_label and model_name"""
        key = f"{app_label}.{model_name}"
        return self._models.get(key)
    
    def get_admin_class(self, app_label, model_name):
        """Get the admin class for a model"""
        key = f"{app_label}.{model_name}"
        return self._admin_classes.get(key)
    
    def get_model_metadata(self, model):
        """Extract comprehensive metadata from a Django model"""
        if not model:
            return None
            
        meta = model._meta
        admin_class = self._admin_classes.get(f"{meta.app_label}.{meta.model_name}")
        
        # Basic model information
        metadata = {
            'app_label': meta.app_label,
            'model_name': meta.model_name,
            'verbose_name': str(meta.verbose_name),
            'verbose_name_plural': str(meta.verbose_name_plural),
            'fields': OrderedDict(),
            'relationships': {},
            'admin_config': {},
            'permissions': [],
            'constraints': []
        }
        
        # Extract field information
        for field in meta.get_fields():
            field_info = self._get_field_metadata(field)
            if field_info:
                metadata['fields'][field.name] = field_info
        
        # Extract relationships
        metadata['relationships'] = self._get_relationships(model)
        
        # Extract admin configuration
        if admin_class:
            metadata['admin_config'] = self._get_admin_metadata(admin_class)
        
        # Extract model constraints
        metadata['constraints'] = self._get_model_constraints(meta)
        
        # Extract model properties (computed fields)
        metadata['properties'] = self._get_model_properties(model)
        
        return metadata
    
    def _get_field_metadata(self, field):
        """Extract metadata for a single field"""
        field_info = {
            'name': field.name,
            'type': field.__class__.__name__,
            'required': not field.null and not field.blank,
            'editable': getattr(field, 'editable', True),
            'help_text': getattr(field, 'help_text', ''),
            'verbose_name': str(getattr(field, 'verbose_name', field.name)),
        }
        
        # Handle different field types
        if hasattr(field, 'max_length') and field.max_length:
            field_info['max_length'] = field.max_length
            
        if hasattr(field, 'choices') and field.choices:
            field_info['choices'] = list(field.choices)
            
        if hasattr(field, 'default') and field.default is not models.NOT_PROVIDED:
            field_info['default'] = field.default
            
        if hasattr(field, 'decimal_places'):
            field_info['decimal_places'] = field.decimal_places
            field_info['max_digits'] = field.max_digits
            
        # Handle relationship fields
        if field.is_relation:
            field_info['related_model'] = {
                'app_label': field.related_model._meta.app_label,
                'model_name': field.related_model._meta.model_name,
                'verbose_name': str(field.related_model._meta.verbose_name)
            }
            
            if hasattr(field, 'limit_choices_to') and field.limit_choices_to:
                field_info['limit_choices_to'] = field.limit_choices_to
        
        return field_info
    
    def _get_relationships(self, model):
        """Extract relationship information"""
        relationships = {
            'foreign_keys': [],
            'reverse_foreign_keys': [],
            'many_to_many': [],
            'one_to_one': []
        }
        
        for field in model._meta.get_fields():
            if field.is_relation:
                rel_info = {
                    'field_name': field.name,
                    'related_model': f"{field.related_model._meta.app_label}.{field.related_model._meta.model_name}",
                    'verbose_name': str(getattr(field, 'verbose_name', field.name))
                }
                
                if isinstance(field, models.ForeignKey):
                    relationships['foreign_keys'].append(rel_info)
                elif hasattr(field, 'related_name') and field.related_name:
                    relationships['reverse_foreign_keys'].append(rel_info)
                elif isinstance(field, models.ManyToManyField):
                    relationships['many_to_many'].append(rel_info)
                elif isinstance(field, models.OneToOneField):
                    relationships['one_to_one'].append(rel_info)
        
        return relationships
    
    def _get_admin_metadata(self, admin_class):
        """Extract admin configuration metadata"""
        admin_config = {}
        
        # Common admin configurations
        for attr in ['list_display', 'list_filter', 'search_fields', 'ordering', 
                     'list_per_page', 'autocomplete_fields', 'readonly_fields']:
            if hasattr(admin_class, attr):
                admin_config[attr] = getattr(admin_class, attr)
        
        # Extract custom actions
        if hasattr(admin_class, 'actions') and admin_class.actions:
            admin_config['actions'] = [action for action in admin_class.actions if action != 'delete_selected']
        
        return admin_config
    
    def _get_model_constraints(self, meta):
        """Extract model constraints"""
        constraints = []
        
        if hasattr(meta, 'constraints'):
            for constraint in meta.constraints:
                constraint_info = {
                    'type': constraint.__class__.__name__,
                    'name': constraint.name,
                }
                
                if hasattr(constraint, 'fields'):
                    constraint_info['fields'] = constraint.fields
                    
                constraints.append(constraint_info)
        
        return constraints
    
    def _get_model_properties(self, model):
        """Extract model properties (computed fields)"""
        properties = []
        
        # Get all properties from the model class
        for name in dir(model):
            if not name.startswith('_'):
                attr = getattr(model, name)
                if isinstance(attr, property):
                    properties.append({
                        'name': name,
                        'type': 'property',
                        'computed': True
                    })
        
        return properties


# Global registry instance
model_registry = ModelRegistry()
