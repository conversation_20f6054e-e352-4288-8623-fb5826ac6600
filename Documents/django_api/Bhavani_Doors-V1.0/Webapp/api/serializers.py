"""
Dynamic Serializers for API
Automatically generates serializers based on model metadata
"""
from rest_framework import serializers
from django.db import models
from .model_registry import model_registry


class DynamicModelSerializer(serializers.ModelSerializer):
    """
    Dynamic serializer that adapts to any model
    """
    
    def __init__(self, *args, **kwargs):
        # Extract model from context or kwargs
        self.model_class = kwargs.pop('model_class', None)
        super().__init__(*args, **kwargs)
        
        if self.model_class:
            self.Meta.model = self.model_class
            self._setup_dynamic_fields()
    
    def _setup_dynamic_fields(self):
        """Setup fields dynamically based on model metadata"""
        if not self.model_class:
            return
            
        metadata = model_registry.get_model_metadata(self.model_class)
        if not metadata:
            return
        
        # Add computed properties as read-only fields
        for prop in metadata.get('properties', []):
            if prop['computed']:
                self.fields[prop['name']] = serializers.ReadOnlyField()
    
    class Meta:
        model = None
        fields = '__all__'
        
    def to_representation(self, instance):
        """Enhanced representation with metadata"""
        data = super().to_representation(instance)
        
        # Add model metadata if requested
        if self.context.get('include_metadata', False):
            metadata = model_registry.get_model_metadata(self.model_class)
            data['_metadata'] = {
                'model_name': metadata['model_name'],
                'verbose_name': metadata['verbose_name'],
                'app_label': metadata['app_label']
            }
        
        return data


class ModelSchemaSerializer(serializers.Serializer):
    """
    Serializer for model schema information
    """
    app_label = serializers.CharField()
    model_name = serializers.CharField()
    verbose_name = serializers.CharField()
    verbose_name_plural = serializers.CharField()
    fields = serializers.DictField()
    relationships = serializers.DictField()
    admin_config = serializers.DictField()
    permissions = serializers.ListField()
    constraints = serializers.ListField()
    properties = serializers.ListField()


class ModelListSerializer(serializers.Serializer):
    """
    Serializer for listing available models
    """
    app_label = serializers.CharField()
    model_name = serializers.CharField()
    verbose_name = serializers.CharField()
    verbose_name_plural = serializers.CharField()
    api_url = serializers.CharField()
    schema_url = serializers.CharField()
    count = serializers.IntegerField(required=False)


def get_serializer_for_model(model_class, **kwargs):
    """
    Factory function to create a serializer for any model
    """
    class DynamicSerializer(DynamicModelSerializer):
        class Meta:
            model = model_class
            fields = '__all__'
    
    return DynamicSerializer(**kwargs)


def get_list_serializer_for_model(model_class, **kwargs):
    """
    Factory function to create a list serializer for any model
    """
    class DynamicListSerializer(DynamicModelSerializer):
        class Meta:
            model = model_class
            fields = '__all__'
        
        def to_representation(self, instance):
            """Simplified representation for list views"""
            data = super().to_representation(instance)
            
            # Add primary key and string representation
            data['id'] = instance.pk
            data['__str__'] = str(instance)
            
            return data
    
    return DynamicListSerializer(**kwargs)


class RelatedFieldSerializer(serializers.Serializer):
    """
    Serializer for related field choices
    """
    id = serializers.IntegerField()
    display_name = serializers.CharField()
    model_name = serializers.CharField()
    
    def to_representation(self, instance):
        return {
            'id': instance.pk,
            'display_name': str(instance),
            'model_name': instance._meta.model_name
        }


class FieldChoicesSerializer(serializers.Serializer):
    """
    Serializer for field choices
    """
    value = serializers.CharField()
    display = serializers.CharField()


class ValidationErrorSerializer(serializers.Serializer):
    """
    Serializer for validation errors
    """
    field = serializers.CharField()
    message = serializers.CharField()
    code = serializers.CharField(required=False)


class APIResponseSerializer(serializers.Serializer):
    """
    Standard API response format
    """
    success = serializers.BooleanField()
    data = serializers.JSONField(required=False)
    errors = ValidationErrorSerializer(many=True, required=False)
    message = serializers.CharField(required=False)
    metadata = serializers.DictField(required=False)
    pagination = serializers.DictField(required=False)
