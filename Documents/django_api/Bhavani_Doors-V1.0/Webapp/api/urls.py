"""
API URL Configuration
Dynamic URL patterns for all models
"""
from django.urls import path, include
from . import views

app_name = 'api'

urlpatterns = [
    # API health check
    path('health/', views.api_health_check, name='api-health'),

    # Model discovery endpoints
    path('models/', views.ModelListView.as_view(), name='model-list'),

    # Model schema endpoints
    path('models/<str:app_label>/<str:model_name>/schema/',
         views.ModelSchemaView.as_view(), name='model-schema'),

    # Model statistics
    path('models/<str:app_label>/<str:model_name>/stats/',
         views.get_model_stats, name='model-stats'),

    # Field choices endpoints
    path('models/<str:app_label>/<str:model_name>/fields/<str:field_name>/choices/',
         views.RelatedFieldChoicesView.as_view(), name='related-field-choices'),

    path('models/<str:app_label>/<str:model_name>/fields/<str:field_name>/options/',
         views.get_field_choices, name='field-choices'),

    # Data validation
    path('models/<str:app_label>/<str:model_name>/validate/',
         views.validate_model_data, name='validate-data'),

    # CRUD endpoints for models
    path('models/<str:app_label>/<str:model_name>/',
         views.DynamicModelListCreateView.as_view(), name='model-list-create'),

    path('models/<str:app_label>/<str:model_name>/<int:pk>/',
         views.DynamicModelDetailView.as_view(), name='model-detail'),
]
