"""
Dynamic API Views
Provides CRUD operations for all models registered in Django Admin
"""
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import ensure_csrf_cookie
from django.http import JsonResponse
from django.core.exceptions import ValidationError
from django.db import transaction
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from rest_framework.pagination import PageNumberPagination
from .model_registry import model_registry
from .serializers import (
    DynamicModelSerializer, ModelSchemaSerializer, ModelListSerializer,
    get_serializer_for_model, get_list_serializer_for_model,
    RelatedFieldSerializer, FieldChoicesSerializer, APIResponseSerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


@method_decorator(ensure_csrf_cookie, name='dispatch')
class ModelListView(APIView):
    """
    API endpoint that lists all available models
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get list of all available models"""
        models = model_registry.get_all_models()
        model_list = []

        for key, model_class in models.items():
            app_label, model_name = key.split('.')
            metadata = model_registry.get_model_metadata(model_class)

            # Get model count
            try:
                count = model_class.objects.count()
            except:
                count = 0

            model_info = {
                'app_label': app_label,
                'model_name': model_name,
                'verbose_name': metadata['verbose_name'],
                'verbose_name_plural': metadata['verbose_name_plural'],
                'api_url': f'/api/models/{app_label}/{model_name}/',
                'schema_url': f'/api/models/{app_label}/{model_name}/schema/',
                'count': count
            }
            model_list.append(model_info)

        # Sort by verbose name
        model_list.sort(key=lambda x: x['verbose_name'])

        serializer = ModelListSerializer(model_list, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'message': f'Found {len(model_list)} models'
        })


@method_decorator(ensure_csrf_cookie, name='dispatch')
class ModelSchemaView(APIView):
    """
    API endpoint that returns schema information for a specific model
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, app_label, model_name):
        """Get schema information for a model"""
        model_class = model_registry.get_model(app_label, model_name)
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {app_label}.{model_name} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        metadata = model_registry.get_model_metadata(model_class)
        serializer = ModelSchemaSerializer(metadata)

        return Response({
            'success': True,
            'data': serializer.data,
            'message': f'Schema for {metadata["verbose_name"]}'
        })


@method_decorator(ensure_csrf_cookie, name='dispatch')
class DynamicModelListCreateView(ListCreateAPIView):
    """
    Dynamic API endpoint for listing and creating model instances
    """
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get_model_class(self):
        """Get the model class from URL parameters"""
        app_label = self.kwargs['app_label']
        model_name = self.kwargs['model_name']
        return model_registry.get_model(app_label, model_name)

    def get_queryset(self):
        """Get queryset for the model"""
        model_class = self.get_model_class()
        if not model_class:
            return model_class.objects.none()

        queryset = model_class.objects.all()

        # Apply admin ordering if available
        admin_class = model_registry.get_admin_class(
            self.kwargs['app_label'],
            self.kwargs['model_name']
        )
        if admin_class and hasattr(admin_class, 'ordering'):
            queryset = queryset.order_by(*admin_class.ordering)

        # Apply search if provided
        search = self.request.query_params.get('search', None)
        if search and admin_class and hasattr(admin_class, 'search_fields'):
            from django.db.models import Q
            search_query = Q()
            for field in admin_class.search_fields:
                search_query |= Q(**{f"{field}__icontains": search})
            queryset = queryset.filter(search_query)

        return queryset

    def get_serializer_class(self):
        """Get dynamic serializer for the model"""
        model_class = self.get_model_class()
        if not model_class:
            return DynamicModelSerializer

        class DynamicListSerializer(DynamicModelSerializer):
            class Meta:
                model = model_class
                fields = '__all__'

            def to_representation(self, instance):
                """Simplified representation for list views"""
                data = super().to_representation(instance)

                # Add primary key and string representation
                data['id'] = instance.pk
                data['__str__'] = str(instance)

                return data

        return DynamicListSerializer

    def list(self, request, *args, **kwargs):
        """Enhanced list response with metadata"""
        model_class = self.get_model_class()
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {kwargs["app_label"]}.{kwargs["model_name"]} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        response = super().list(request, *args, **kwargs)

        # Add metadata to response
        metadata = model_registry.get_model_metadata(model_class)

        return Response({
            'success': True,
            'data': response.data['results'],
            'pagination': {
                'count': response.data['count'],
                'next': response.data['next'],
                'previous': response.data['previous'],
                'page_size': self.pagination_class.page_size
            },
            'metadata': {
                'model_name': metadata['model_name'],
                'verbose_name': metadata['verbose_name'],
                'verbose_name_plural': metadata['verbose_name_plural'],
                'fields': metadata['fields'],
                'admin_config': metadata['admin_config']
            }
        })

    def create(self, request, *args, **kwargs):
        """Enhanced create response"""
        model_class = self.get_model_class()
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {kwargs["app_label"]}.{kwargs["model_name"]} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            with transaction.atomic():
                # Create dynamic serializer class
                class DynamicCreateSerializer(DynamicModelSerializer):
                    class Meta:
                        model = model_class
                        fields = '__all__'

                serializer = DynamicCreateSerializer(data=request.data)
                if serializer.is_valid():
                    instance = serializer.save()
                    return Response({
                        'success': True,
                        'data': serializer.data,
                        'message': f'{model_class._meta.verbose_name} created successfully'
                    }, status=status.HTTP_201_CREATED)
                else:
                    return Response({
                        'success': False,
                        'errors': serializer.errors,
                        'message': 'Validation failed'
                    }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error creating {model_class._meta.verbose_name}: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(ensure_csrf_cookie, name='dispatch')
class DynamicModelDetailView(RetrieveUpdateDestroyAPIView):
    """
    Dynamic API endpoint for retrieving, updating, and deleting model instances
    """
    permission_classes = [IsAuthenticated]

    def get_model_class(self):
        """Get the model class from URL parameters"""
        app_label = self.kwargs['app_label']
        model_name = self.kwargs['model_name']
        return model_registry.get_model(app_label, model_name)

    def get_object(self):
        """Get the specific object"""
        model_class = self.get_model_class()
        if not model_class:
            return None

        pk = self.kwargs['pk']
        return get_object_or_404(model_class, pk=pk)

    def get_serializer_class(self):
        """Get dynamic serializer for the model"""
        model_class = self.get_model_class()
        if not model_class:
            return DynamicModelSerializer

        class DynamicDetailSerializer(DynamicModelSerializer):
            class Meta:
                model = model_class
                fields = '__all__'

        return DynamicDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        """Enhanced retrieve response"""
        model_class = self.get_model_class()
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {kwargs["app_label"]}.{kwargs["model_name"]} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return Response({
            'success': True,
            'data': serializer.data,
            'message': f'{model_class._meta.verbose_name} retrieved successfully'
        })

    def update(self, request, *args, **kwargs):
        """Enhanced update response"""
        model_class = self.get_model_class()
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {kwargs["app_label"]}.{kwargs["model_name"]} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            with transaction.atomic():
                partial = kwargs.pop('partial', False)
                instance = self.get_object()
                serializer = self.get_serializer(instance, data=request.data, partial=partial)

                if serializer.is_valid():
                    serializer.save()
                    return Response({
                        'success': True,
                        'data': serializer.data,
                        'message': f'{model_class._meta.verbose_name} updated successfully'
                    })
                else:
                    return Response({
                        'success': False,
                        'errors': serializer.errors,
                        'message': 'Validation failed'
                    }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error updating {model_class._meta.verbose_name}: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        """Enhanced delete response"""
        model_class = self.get_model_class()
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {kwargs["app_label"]}.{kwargs["model_name"]} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            with transaction.atomic():
                instance = self.get_object()
                instance.delete()
                return Response({
                    'success': True,
                    'message': f'{model_class._meta.verbose_name} deleted successfully'
                }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error deleting {model_class._meta.verbose_name}: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(ensure_csrf_cookie, name='dispatch')
class RelatedFieldChoicesView(APIView):
    """
    API endpoint for getting choices for related fields
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, app_label, model_name, field_name):
        """Get choices for a related field"""
        model_class = model_registry.get_model(app_label, model_name)
        if not model_class:
            return Response({
                'success': False,
                'message': f'Model {app_label}.{model_name} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            field = model_class._meta.get_field(field_name)
            if not field.is_relation:
                return Response({
                    'success': False,
                    'message': f'Field {field_name} is not a related field'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get related model choices
            related_model = field.related_model
            queryset = related_model.objects.all()

            # Apply limit_choices_to if present
            if hasattr(field, 'limit_choices_to') and field.limit_choices_to:
                queryset = queryset.filter(**field.limit_choices_to)

            # Apply search if provided
            search = request.query_params.get('search', None)
            if search:
                # Try to search in common fields
                search_fields = ['name', 'title', '__str__']
                from django.db.models import Q
                search_query = Q()
                for search_field in search_fields:
                    try:
                        if hasattr(related_model, search_field.replace('__str__', '')):
                            search_query |= Q(**{f"{search_field}__icontains": search})
                    except:
                        pass
                if search_query:
                    queryset = queryset.filter(search_query)

            # Limit results
            limit = int(request.query_params.get('limit', 50))
            queryset = queryset[:limit]

            serializer = RelatedFieldSerializer(queryset, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'message': f'Choices for {field_name}'
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error getting choices for {field_name}: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_field_choices(request, app_label, model_name, field_name):
    """
    Get choices for fields with predefined choices
    """
    model_class = model_registry.get_model(app_label, model_name)
    if not model_class:
        return Response({
            'success': False,
            'message': f'Model {app_label}.{model_name} not found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        field = model_class._meta.get_field(field_name)
        if not hasattr(field, 'choices') or not field.choices:
            return Response({
                'success': False,
                'message': f'Field {field_name} does not have predefined choices'
            }, status=status.HTTP_400_BAD_REQUEST)

        choices = [{'value': choice[0], 'display': choice[1]} for choice in field.choices]

        return Response({
            'success': True,
            'data': choices,
            'message': f'Choices for {field_name}'
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error getting choices for {field_name}: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_model_data(request, app_label, model_name):
    """
    Validate model data without saving
    """
    model_class = model_registry.get_model(app_label, model_name)
    if not model_class:
        return Response({
            'success': False,
            'message': f'Model {app_label}.{model_name} not found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        serializer = get_serializer_for_model(model_class, data=request.data)
        if serializer.is_valid():
            return Response({
                'success': True,
                'message': 'Data is valid',
                'data': serializer.validated_data
            })
        else:
            return Response({
                'success': False,
                'errors': serializer.errors,
                'message': 'Validation failed'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error validating data: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_model_stats(request, app_label, model_name):
    """
    Get statistics for a model
    """
    model_class = model_registry.get_model(app_label, model_name)
    if not model_class:
        return Response({
            'success': False,
            'message': f'Model {app_label}.{model_name} not found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        stats = {
            'total_count': model_class.objects.count(),
            'model_name': model_class._meta.model_name,
            'verbose_name': str(model_class._meta.verbose_name),
            'verbose_name_plural': str(model_class._meta.verbose_name_plural),
        }

        # Add field-specific stats if requested
        if request.query_params.get('include_field_stats', '').lower() == 'true':
            field_stats = {}
            for field in model_class._meta.get_fields():
                if not field.is_relation and hasattr(field, 'choices') and field.choices:
                    # Get choice distribution
                    from django.db.models import Count
                    choice_stats = model_class.objects.values(field.name).annotate(
                        count=Count(field.name)
                    ).order_by('-count')
                    field_stats[field.name] = list(choice_stats)

            stats['field_stats'] = field_stats

        return Response({
            'success': True,
            'data': stats,
            'message': f'Statistics for {model_class._meta.verbose_name}'
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error getting statistics: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_health_check(request):
    """
    API health check endpoint
    """
    try:
        models_count = len(model_registry.get_all_models())
        return Response({
            'success': True,
            'data': {
                'status': 'healthy',
                'models_available': models_count,
                'user_authenticated': request.user.is_authenticated,
                'user': str(request.user) if request.user.is_authenticated else None
            },
            'message': 'API is healthy'
        })
    except Exception as e:
        return Response({
            'success': False,
            'message': f'API health check failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
