<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bhavani Doors API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .put { background-color: #ffc107; color: #212529; }
        .delete { background-color: #dc3545; }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .description {
            margin-top: 10px;
            color: #6c757d;
        }
        .test-section {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .model-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        .model-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .model-endpoint {
            font-family: monospace;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚪 Bhavani Doors REST API</h1>
        
        <div class="success">
            <strong>✅ API Successfully Implemented!</strong><br>
            The REST API has been successfully created and is exposing all Django Admin models dynamically.
        </div>

        <div class="warning">
            <strong>🔐 Authentication Required</strong><br>
            This API uses session-based authentication. You must first login through the Django Admin panel at 
            <a href="/admin/">/admin/</a> before accessing API endpoints.
        </div>

        <h2>🌐 Global Endpoints</h2>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/health/</span>
            <div class="description">API health check and authentication status</div>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/</span>
            <div class="description">List all available models with metadata</div>
        </div>

        <h2>📊 Model-Specific Endpoints</h2>
        <p>For each model <code>{app_label}/{model_name}</code>, the following endpoints are available:</p>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/{app_label}/{model_name}/schema/</span>
            <div class="description">Get complete model schema with field information</div>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/{app_label}/{model_name}/</span>
            <div class="description">List model instances with pagination</div>
        </div>

        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/models/{app_label}/{model_name}/</span>
            <div class="description">Create new model instance</div>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/{app_label}/{model_name}/{id}/</span>
            <div class="description">Get specific model instance</div>
        </div>

        <div class="endpoint">
            <span class="method put">PUT</span>
            <span class="url">/api/models/{app_label}/{model_name}/{id}/</span>
            <div class="description">Update model instance</div>
        </div>

        <div class="endpoint">
            <span class="method delete">DELETE</span>
            <span class="url">/api/models/{app_label}/{model_name}/{id}/</span>
            <div class="description">Delete model instance</div>
        </div>

        <h2>🔧 Utility Endpoints</h2>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/{app_label}/{model_name}/stats/</span>
            <div class="description">Get model statistics and counts</div>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/models/{app_label}/{model_name}/fields/{field_name}/choices/</span>
            <div class="description">Get choices for related fields</div>
        </div>

        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/models/{app_label}/{model_name}/validate/</span>
            <div class="description">Validate model data without saving</div>
        </div>

        <h2>📋 Available Models</h2>
        <p>The API automatically discovered and exposed <strong>20 models</strong> from the Django Admin:</p>

        <div class="models-grid">
            <div class="model-card">
                <div class="model-name">Materials</div>
                <div class="model-endpoint">/api/models/bhavani_doors/material/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Customers</div>
                <div class="model-endpoint">/api/models/bhavani_doors/customer/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Suppliers</div>
                <div class="model-endpoint">/api/models/bhavani_doors/supplier/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Orders</div>
                <div class="model-endpoint">/api/models/bhavani_doors/order/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Stock Orders</div>
                <div class="model-endpoint">/api/models/bhavani_doors/stock_order/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Designs</div>
                <div class="model-endpoint">/api/models/bhavani_doors/design/</div>
            </div>
            <div class="model-card">
                <div class="model-name">Units</div>
                <div class="model-endpoint">/api/models/bhavani_doors/unit/</div>
            </div>
            <div class="model-card">
                <div class="model-name">GST Orders</div>
                <div class="model-endpoint">/api/models/bhavani_doors/gst_order/</div>
            </div>
        </div>

        <h2>🧪 Test Results</h2>
        <div class="test-section">
            <h3>✅ API Test Results</h3>
            <ul>
                <li>✅ Model Registry: Found 20 models</li>
                <li>✅ Health Check: API is healthy</li>
                <li>✅ Models List: 20 models available</li>
                <li>✅ Schema Endpoint: Material schema with 29 fields</li>
                <li>✅ Data Endpoint: 825 material records accessible</li>
            </ul>
        </div>

        <h2>🚀 Next Steps</h2>
        <div class="code">
1. Login to Django Admin: <a href="/admin/">/admin/</a>
2. Test API endpoints using your preferred tool (Postman, curl, etc.)
3. Integrate with your Angular application
4. Generate full documentation: python manage.py generate_api_docs
        </div>

        <h2>📖 Example Usage</h2>
        <div class="code">
// Get all materials
fetch('/api/models/bhavani_doors/material/')
  .then(response => response.json())
  .then(data => console.log(data));

// Get material schema for form generation
fetch('/api/models/bhavani_doors/material/schema/')
  .then(response => response.json())
  .then(schema => console.log(schema.data.fields));

// Create new material
fetch('/api/models/bhavani_doors/material/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRFToken': csrfToken
  },
  body: JSON.stringify({
    name: "New Door",
    design: 1,
    unit: 1,
    unit_order_price: 150.00
  })
});
        </div>

        <div class="success">
            <strong>🎉 Implementation Complete!</strong><br>
            The REST API is fully functional and ready for Angular integration. All requirements have been met:
            <ul>
                <li>✅ Dynamic model discovery</li>
                <li>✅ Session-based authentication</li>
                <li>✅ CSRF protection</li>
                <li>✅ Rich metadata for UI generation</li>
                <li>✅ Angular-ready response formats</li>
            </ul>
        </div>
    </div>
</body>
</html>
