from django.core.management.base import BaseCommand
from datetime import timedelta
from django.utils import timezone
from django.db import transaction
from bhavani_doors.models import (
    order, order_history, order_item_history,
    customer_payment, customer_payment_history,
    supplier_payment, supplier_payment_history,
    stock_return, stock_return_history,
    order_return, order_return_history,
    customer, supplier, material,
    stock_order, stock_order_history, stock_order_item, stock_order_item_history
)

class Command(BaseCommand):
    help = 'Archives all entities older than 30 days with event tracking and versioning'

    def handle(self, *args, **options):
        cutoff_date = timezone.now() - timedelta(days=30)
        with transaction.atomic():
            # Archive orders
            self.archive_orders(cutoff_date)
            # Archive stock orders
            self.archive_stock_orders(cutoff_date)
            # Archive payments and returns
            self.archive_customer_payments(cutoff_date)
            self.archive_supplier_payments(cutoff_date)
            self.archive_stock_returns(cutoff_date)
            self.archive_order_returns(cutoff_date)

    def archive_stock_orders(self, cutoff_date):
        from bhavani_doors.models import stock_order, stock_order_history, stock_order_item, stock_order_item_history
        stock_orders = stock_order.objects.filter(date__lt=cutoff_date, status='delivered')
        for so in stock_orders:
            supplier = so.supplier
            supplier.hst_total_order_amt += so.total_amt
            supplier.hst_transport_charges += so.transport_charges
            supplier.hst_unloading_charges += so.unloading_charges
            supplier.save()
            so_history = stock_order_history.objects.create(
                order_id=so.order_id,
                entry_type=so.entry_type,
                supplier=so.supplier,
                stock_date=so.date,
                status=so.status,
                transport_charges=so.transport_charges,
                unloading_charges=so.unloading_charges,
                notes=so.notes,
                archival_date=timezone.now()
            )
            items = list(so.stock_order_item_set.all())
            for item in items:
                # Update material hst_stock_total_order_amt and hst_stock_total_order_qnty
                item.material.hst_stock_total_order_amt += item.total_price
                item.material.hst_stock_total_order_qnty += item.quantity * item.unit.qnty
                item.material.save()
                stock_order_item_history.objects.create(
                    order=so_history,
                    entry_type=so.entry_type,
                    item_no=item.item_no,
                    material=item.material,
                    unit=item.unit,
                    quantity=item.quantity,
                    unit_price=item.unit_price,
                    archival_date=timezone.now()
                )
                item.delete()
            so.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {stock_orders.count()} stock orders'))

    def archive_orders(self, cutoff_date):
        orders = order.objects.filter(date__lt=cutoff_date, status='delivered')
        for order_obj in orders:
            customer = order_obj.customer
            customer.hst_total_order_amt += order_obj.total_amt
            customer.hst_total_freight += order_obj.freight
            customer.hst_total_profit += order_obj.total_profit
            customer.save()
            order_history_obj = order_history.objects.create(
                order_id=order_obj.order_id,
                customer=order_obj.customer,
                date=order_obj.date,
                status=order_obj.status,
                freight=order_obj.freight,
                entry_type=order_obj.entry_type,
                version=order_obj.version,
                parent_id=order_obj.parent_id,
                archival_date=timezone.now()
            )
            items = list(order_obj.order_item_set.all())
            for item in items:
                # Update material hst_order_total_order_amt and hst_order_total_order_qnty
                item.material.hst_order_total_order_amt += item.total_price
                item.material.hst_order_total_order_qnty += item.quantity * item.unit.qnty
                item.material.hst_order_total_profit += item.total_profit
                item.material.save()
                existing_history = order_item_history.objects.filter(
                    order=order_history_obj,
                    entry_type=order_obj.entry_type,
                    item_no=item.item_no
                ).first()
                if not existing_history:
                    order_item_history.objects.create(
                        order=order_history_obj,
                        entry_type=order_obj.entry_type,
                        item_no=item.item_no,
                        material=item.material,
                        unit=item.unit,
                        quantity=item.quantity,
                        unit_price=item.unit_price,
                        archival_date=timezone.now()
                    )
                item.delete()
            order_obj.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {orders.count()} orders'))

    def archive_customer_payments(self, cutoff_date):
        payments = customer_payment.objects.filter(date__lt=cutoff_date)
        for payment in payments:
            customer = payment.customer
            customer.hst_total_payment += payment.amount
            customer.hst_total_discount += payment.discount
            customer.save()
            customer_payment_history.objects.create(
                customer=payment.customer,
                date=payment.date,
                amount=payment.amount,
                discount=payment.discount,
                trans_type=payment.trans_type,
                ref_no=payment.ref_no,
                entry_type=getattr(payment, 'entry_type', 'new'),
                version=getattr(payment, 'version', 0),
                parent_id=getattr(payment, 'parent_id', None),
                archival_date=timezone.now()
            )
            payment.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {payments.count()} customer payments'))

    def archive_supplier_payments(self, cutoff_date):
        payments = supplier_payment.objects.filter(date__lt=cutoff_date)
        for payment in payments:
            supplier = payment.supplier
            supplier.hst_total_payment += payment.amount
            supplier.save()
            supplier_payment_history.objects.create(
                supplier=payment.supplier,
                date=payment.date,
                amount=payment.amount,
                trans_type=payment.trans_type,
                ref_no=payment.ref_no,
                entry_type=getattr(payment, 'entry_type', 'new'),
                version=getattr(payment, 'version', 0),
                parent_id=getattr(payment, 'parent_id', None),
                archival_date=timezone.now()
            )
            payment.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {payments.count()} supplier payments'))

    def archive_stock_returns(self, cutoff_date):
        returns = stock_return.objects.filter(date__lt=cutoff_date)
        for ret in returns:
            supplier = ret.supplier
            # Update material hst_stock_total_return_amt and hst_stock_total_return_qnty
            ret.material.hst_stock_total_return_amt += ret.total_price
            ret.material.hst_stock_total_return_qnty += ret.quantity * ret.unit.qnty
            ret.material.save()
            stock_return_history.objects.create(
                supplier=ret.supplier,
                date=ret.date,
                material=ret.material,
                unit=ret.unit,
                quantity=ret.quantity,
                unit_price=ret.unit_price,
                entry_type=getattr(ret, 'entry_type', 'new'),
                version=getattr(ret, 'version', 0),
                parent_id=getattr(ret, 'parent_id', None),
                archival_date=timezone.now()
            )
            ret.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {returns.count()} stock returns'))

    def archive_order_returns(self, cutoff_date):
        returns = order_return.objects.filter(date__lt=cutoff_date)
        for ret in returns:
            customer = ret.customer
            # Update material hst_order_total_return_amt and hst_order_total_return_qnty
            ret.material.hst_order_total_return_amt += ret.total_price
            ret.material.hst_order_total_return_qnty += ret.quantity * ret.unit.qnty
            ret.material.save()
            customer.hst_total_order_return_amt += ret.total_price
            customer.save()
            order_return_history.objects.create(
                customer=ret.customer,
                date=ret.date,
                material=ret.material,
                unit=ret.unit,
                quantity=ret.quantity,
                unit_price=ret.unit_price,
                entry_type=getattr(ret, 'entry_type', 'new'),
                version=getattr(ret, 'version', 0),
                parent_id=getattr(ret, 'parent_id', None),
                archival_date=timezone.now()
            )
            ret.delete()
        self.stdout.write(self.style.SUCCESS(f'Archived {returns.count()} order returns'))
