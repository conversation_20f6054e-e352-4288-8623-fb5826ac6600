# Generated by Django 5.1.6 on 2025-02-20 12:05

import datetime
import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='gst_tax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('next_invoice_no', models.PositiveIntegerField(default=0)),
                ('cgst', models.DecimalField(decimal_places=2, max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('sgst', models.DecimalField(decimal_places=2, max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('igst', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
            ],
            options={
                'verbose_name_plural': 'E.3 GST Tax',
            },
        ),
        migrations.CreateModel(
            name='customer_type',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(max_length=30)),
            ],
            options={
                'verbose_name_plural': 'B.5 Customer Types',
                'constraints': [models.UniqueConstraint(fields=('type',), name='unique_customer_type')],
            },
        ),
        migrations.CreateModel(
            name='customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
                ('date', models.DateTimeField(auto_now=True)),
                ('phone', models.CharField(max_length=10)),
                ('address', models.TextField()),
                ('opening_amt', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer_type')),
            ],
            options={
                'verbose_name_plural': 'b.0 Customers',
            },
        ),
        migrations.CreateModel(
            name='design',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=15)),
                ('color', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'verbose_name_plural': 'c.0 Designs',
                'constraints': [models.UniqueConstraint(fields=('name', 'color'), name='unique_design')],
            },
        ),
        migrations.CreateModel(
            name='gst_material',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
                ('hsncode', models.CharField(max_length=10)),
            ],
            options={
                'verbose_name_plural': 'E.0 Materials',
                'constraints': [models.UniqueConstraint(fields=('name',), name='unique_gst_material')],
            },
        ),
        migrations.CreateModel(
            name='gst_order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_no', models.PositiveSmallIntegerField(default=0)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('open', 'Open'), ('delivered', 'Delivered')], default='open', max_length=20)),
                ('customer', models.CharField(max_length=30)),
                ('gst_no', models.CharField(blank=True, max_length=15, null=True)),
                ('vehicle_no', models.CharField(blank=True, max_length=15, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('cgst', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=4)),
                ('sgst', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=4)),
                ('igst', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=4)),
            ],
            options={
                'verbose_name_plural': 'E.1 Orders',
                'constraints': [models.UniqueConstraint(fields=('invoice_no',), name='unique_gst_order')],
            },
        ),
        migrations.CreateModel(
            name='material_type',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
            ],
            options={
                'verbose_name_plural': 'A. Current Stock Value',
                'constraints': [models.UniqueConstraint(fields=('name',), name='unique_material_type')],
            },
        ),
        migrations.CreateModel(
            name='material',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
                ('sales_type', models.CharField(choices=[('single', 'Single'), ('group', 'Group')], default='single', max_length=15)),
                ('unit_order_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('opening_quantity', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=12)),
                ('opening_unit_stock_price', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=12)),
                ('opening_unit_order_price', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=12)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('design', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.design')),
                ('type', models.ForeignKey(default=2, on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material_type')),
            ],
            options={
                'verbose_name_plural': 'd.0 Materials',
            },
        ),
        migrations.CreateModel(
            name='order',
            fields=[
                ('order_id', models.CharField(default='', editable=False, max_length=25, primary_key=True, serialize=False)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('open', 'Open'), ('delivered', 'Delivered')], default='open', max_length=20)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer')),
            ],
            options={
                'verbose_name_plural': 'B.1 Orders',
            },
        ),
        migrations.CreateModel(
            name='supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
                ('date', models.DateTimeField(auto_now=True)),
                ('phone', models.CharField(max_length=10)),
                ('address', models.TextField()),
                ('opening_amt', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
            ],
            options={
                'verbose_name_plural': 'a.0 Suppliers',
                'constraints': [models.UniqueConstraint(fields=('name', 'phone'), name='unique_supplier')],
            },
        ),
        migrations.CreateModel(
            name='stock_order',
            fields=[
                ('order_id', models.CharField(default='', editable=False, max_length=25, primary_key=True, serialize=False)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('open', 'Open'), ('delivered', 'Delivered')], default='open', max_length=20)),
                ('transport_charges', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('unloading_charges', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier')),
            ],
            options={
                'verbose_name_plural': 'A.1 Stock Orders',
            },
        ),
        migrations.CreateModel(
            name='supplier_payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('trans_type', models.CharField(choices=[('cash', 'Cash'), ('online', 'Online')], default='cash', max_length=20)),
                ('ref_no', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier')),
            ],
            options={
                'verbose_name_plural': 'A.4 Supplier Payments',
            },
        ),
        migrations.CreateModel(
            name='unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=15)),
                ('qnty', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
            ],
            options={
                'verbose_name_plural': 'c.1 Units',
                'constraints': [models.UniqueConstraint(fields=('name', 'qnty'), name='unique_unit')],
            },
        ),
        migrations.CreateModel(
            name='stock_return',
            fields=[
                ('sno', models.PositiveIntegerField(default=0, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'A.3 Stock Return',
            },
        ),
        migrations.CreateModel(
            name='stock_order_item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_no', models.SmallIntegerField(default=0, editable=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material')),
                ('order', models.ForeignKey(limit_choices_to={'status': 'open'}, on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.stock_order')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'A.2 Stock Order Items',
            },
        ),
        migrations.CreateModel(
            name='order_return',
            fields=[
                ('sno', models.PositiveIntegerField(default=0, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'B.3 Order Returns',
            },
        ),
        migrations.CreateModel(
            name='order_item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_no', models.SmallIntegerField(default=0, editable=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='order_item_set', to='bhavani_doors.material')),
                ('order', models.ForeignKey(limit_choices_to={'status': 'open'}, on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.order')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'B.2 Order Items',
            },
        ),
        migrations.AddField(
            model_name='material',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit'),
        ),
        migrations.CreateModel(
            name='gst_order_item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_no', models.SmallIntegerField(default=0, editable=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='gst_order_item', to='bhavani_doors.gst_material')),
                ('order', models.ForeignKey(limit_choices_to={'status': 'open'}, on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.gst_order')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'E.2 Order Items',
            },
        ),
        migrations.CreateModel(
            name='group_materials_item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qnty', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('group', models.ForeignKey(limit_choices_to={'sales_type': 'group'}, on_delete=django.db.models.deletion.RESTRICT, related_name='grp_materials_items', to='bhavani_doors.material')),
                ('item', models.ForeignKey(limit_choices_to={'sales_type': 'single'}, on_delete=django.db.models.deletion.RESTRICT, related_name='single_materials_items', to='bhavani_doors.material')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit')),
            ],
            options={
                'verbose_name_plural': 'd.2 Group Material Items',
            },
        ),
        migrations.CreateModel(
            name='customer_payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('trans_type', models.CharField(choices=[('cash', 'Cash'), ('online', 'Online')], default='cash', max_length=20)),
                ('ref_no', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer')),
            ],
            options={
                'verbose_name_plural': 'B.4 Customer Payments',
                'constraints': [models.UniqueConstraint(fields=('amount', 'date'), name='unique_customer_payment')],
            },
        ),
        migrations.AddConstraint(
            model_name='customer',
            constraint=models.UniqueConstraint(fields=('name', 'phone'), name='unique_customer'),
        ),
        migrations.AddConstraint(
            model_name='stock_order',
            constraint=models.UniqueConstraint(fields=('order_id', 'supplier'), name='unique_stock_order'),
        ),
        migrations.AddConstraint(
            model_name='supplier_payment',
            constraint=models.UniqueConstraint(fields=('amount', 'date'), name='unique_supplier_payment'),
        ),
        migrations.AddConstraint(
            model_name='stock_return',
            constraint=models.UniqueConstraint(fields=('supplier', 'date', 'material', 'unit', 'quantity', 'unit_price'), name='unique_stock_order_return'),
        ),
        migrations.AddConstraint(
            model_name='stock_order_item',
            constraint=models.UniqueConstraint(fields=('order', 'material', 'quantity', 'unit_price'), name='unique_stock_order_item'),
        ),
        migrations.AddConstraint(
            model_name='order_return',
            constraint=models.UniqueConstraint(fields=('customer', 'date', 'material', 'unit', 'quantity', 'unit_price'), name='unique_order_return'),
        ),
        migrations.AddConstraint(
            model_name='order_item',
            constraint=models.UniqueConstraint(fields=('order', 'material', 'quantity', 'unit_price'), name='unique_order_item'),
        ),
        migrations.AddConstraint(
            model_name='material',
            constraint=models.UniqueConstraint(fields=('name', 'design'), name='unique_material'),
        ),
        migrations.AddConstraint(
            model_name='gst_order_item',
            constraint=models.UniqueConstraint(fields=('order', 'material', 'quantity', 'unit_price'), name='unique_gst_order_item'),
        ),
        migrations.AddConstraint(
            model_name='group_materials_item',
            constraint=models.UniqueConstraint(fields=('group', 'item'), name='unique_group_materials_item'),
        ),
    ]
