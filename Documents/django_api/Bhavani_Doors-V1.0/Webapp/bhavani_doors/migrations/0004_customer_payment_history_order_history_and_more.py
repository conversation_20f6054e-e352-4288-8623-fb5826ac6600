# Generated by Django 5.2.2 on 2025-06-07 18:19

import datetime
import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bhavani_doors', '0003_rename_frieght_order_freight'),
    ]

    operations = [
        migrations.CreateModel(
            name='customer_payment_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('trans_type', models.CharField(max_length=20)),
                ('ref_no', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('entry_type', models.CharField(default='new', max_length=20)),
                ('version', models.PositiveIntegerField(default=0)),
                ('parent_id', models.CharField(blank=True, max_length=25, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'B.3 Customer Payments History',
            },
        ),
        migrations.CreateModel(
            name='order_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(default='', editable=False, max_length=25)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('open', 'Open'), ('delivered', 'Delivered')], default='open', max_length=20)),
                ('freight', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('entry_type', models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20)),
                ('version', models.PositiveIntegerField(default=0)),
                ('parent_id', models.CharField(blank=True, max_length=25, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'B.1 Orders History',
            },
        ),
        migrations.CreateModel(
            name='order_item_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_type', models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20)),
                ('item_no', models.SmallIntegerField(default=0, editable=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'B.2 Order Items History',
            },
        ),
        migrations.CreateModel(
            name='order_return_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('entry_type', models.CharField(default='new', max_length=20)),
                ('version', models.PositiveIntegerField(default=0)),
                ('parent_id', models.CharField(blank=True, max_length=25, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'B.4 Order Return History',
            },
        ),
        migrations.CreateModel(
            name='stock_order_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(default='', editable=False, max_length=25)),
                ('entry_type', models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20)),
                ('stock_date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('open', 'Open'), ('delivered', 'Delivered')], default='open', max_length=20)),
                ('transport_charges', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('unloading_charges', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'A.1 Stock Orders History',
            },
        ),
        migrations.CreateModel(
            name='stock_order_item_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_type', models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20)),
                ('item_no', models.SmallIntegerField(default=0, editable=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'A.2 Stock Order Items History',
            },
        ),
        migrations.CreateModel(
            name='stock_return_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=12)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('entry_type', models.CharField(default='new', max_length=20)),
                ('version', models.PositiveIntegerField(default=0)),
                ('parent_id', models.CharField(blank=True, max_length=25, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'A.3 Stock Return History',
            },
        ),
        migrations.CreateModel(
            name='supplier_payment_history',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('trans_type', models.CharField(max_length=20)),
                ('ref_no', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('entry_type', models.CharField(default='new', max_length=20)),
                ('version', models.PositiveIntegerField(default=0)),
                ('parent_id', models.CharField(blank=True, max_length=25, null=True)),
                ('archival_date', models.DateTimeField(default=datetime.datetime.now)),
            ],
            options={
                'verbose_name_plural': 'A.4 Supplier Payments History',
            },
        ),
        migrations.RemoveConstraint(
            model_name='stock_order',
            name='unique_stock_order',
        ),
        migrations.RemoveField(
            model_name='material',
            name='opening_quantity',
        ),
        migrations.RemoveField(
            model_name='material',
            name='opening_unit_order_price',
        ),
        migrations.RemoveField(
            model_name='material',
            name='opening_unit_stock_price',
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_discount',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_freight',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_order_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_order_return_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_payment',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='customer',
            name='hst_total_profit',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_order_total_order_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_order_total_order_qnty',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_order_total_profit',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_order_total_return_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_order_total_return_qnty',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_stock_total_order_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_stock_total_order_qnty',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_stock_total_return_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='hst_stock_total_return_qnty',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='material',
            name='opening_stock_quantity',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AddField(
            model_name='material',
            name='opening_stock_unit_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AddField(
            model_name='order',
            name='entry_type',
            field=models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20),
        ),
        migrations.AddField(
            model_name='order',
            name='parent_id',
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='version',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='order_return',
            name='entry_type',
            field=models.CharField(default='new', max_length=20),
        ),
        migrations.AddField(
            model_name='order_return',
            name='parent_id',
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AddField(
            model_name='order_return',
            name='version',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='stock_order',
            name='entry_type',
            field=models.CharField(choices=[('new', 'New'), ('change', 'Change'), ('delete', 'Delete'), ('revert', 'Revert')], default='new', max_length=20),
        ),
        migrations.AddField(
            model_name='supplier',
            name='hst_total_order_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='supplier',
            name='hst_total_payment',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='supplier',
            name='hst_total_return_amt',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='supplier',
            name='hst_transport_charges',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddField(
            model_name='supplier',
            name='hst_unloading_charges',
            field=models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=56),
        ),
        migrations.AddConstraint(
            model_name='stock_order',
            constraint=models.UniqueConstraint(fields=('order_id', 'entry_type'), name='unique_stock_order'),
        ),
        migrations.AddField(
            model_name='customer_payment_history',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer'),
        ),
        migrations.AddField(
            model_name='order_history',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer'),
        ),
        migrations.AddField(
            model_name='order_item_history',
            name='material',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material'),
        ),
        migrations.AddField(
            model_name='order_item_history',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.order_history'),
        ),
        migrations.AddField(
            model_name='order_item_history',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit'),
        ),
        migrations.AddField(
            model_name='order_return_history',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.customer'),
        ),
        migrations.AddField(
            model_name='order_return_history',
            name='material',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material'),
        ),
        migrations.AddField(
            model_name='order_return_history',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit'),
        ),
        migrations.AddField(
            model_name='stock_order_history',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier'),
        ),
        migrations.AddField(
            model_name='stock_order_item_history',
            name='material',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material'),
        ),
        migrations.AddField(
            model_name='stock_order_item_history',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.stock_order_history'),
        ),
        migrations.AddField(
            model_name='stock_order_item_history',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit'),
        ),
        migrations.AddField(
            model_name='stock_return_history',
            name='material',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.material'),
        ),
        migrations.AddField(
            model_name='stock_return_history',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier'),
        ),
        migrations.AddField(
            model_name='stock_return_history',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.unit'),
        ),
        migrations.AddField(
            model_name='supplier_payment_history',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='bhavani_doors.supplier'),
        ),
        migrations.AddConstraint(
            model_name='customer_payment_history',
            constraint=models.UniqueConstraint(fields=('customer', 'date', 'amount', 'entry_type'), name='unique_customer_payment_history'),
        ),
        migrations.AddConstraint(
            model_name='order_history',
            constraint=models.UniqueConstraint(fields=('order_id', 'entry_type'), name='unique_order_history'),
        ),
        migrations.AddConstraint(
            model_name='order_item_history',
            constraint=models.UniqueConstraint(fields=('order', 'entry_type', 'item_no'), name='unique_order_item_history'),
        ),
        migrations.AddConstraint(
            model_name='order_return_history',
            constraint=models.UniqueConstraint(fields=('customer', 'date', 'material', 'unit', 'quantity', 'unit_price', 'entry_type'), name='unique_order_return_history'),
        ),
        migrations.AddConstraint(
            model_name='stock_order_history',
            constraint=models.UniqueConstraint(fields=('order_id', 'entry_type'), name='unique_stock_order_history'),
        ),
        migrations.AddConstraint(
            model_name='stock_order_item_history',
            constraint=models.UniqueConstraint(fields=('order_id', 'entry_type', 'item_no'), name='unique_stock_order_item_history'),
        ),
        migrations.AddConstraint(
            model_name='stock_return_history',
            constraint=models.UniqueConstraint(fields=('supplier', 'date', 'material', 'unit', 'quantity', 'unit_price', 'entry_type'), name='unique_stock_return_history'),
        ),
        migrations.AddConstraint(
            model_name='supplier_payment_history',
            constraint=models.UniqueConstraint(fields=('supplier', 'date', 'amount', 'entry_type'), name='unique_supplier_payment_history'),
        ),
    ]
