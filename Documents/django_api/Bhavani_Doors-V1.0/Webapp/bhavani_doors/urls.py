from django.urls import include, path
from django.contrib import admin

from . import views

urlpatterns = [  
    path('autofillprice/', include([
        path('get_material_order_price/', views.get_material_order_price, name='get_material_order_price'),
        path('get_material_unit/', views.get_material_unit, name='get_material_unit'),          
    ])),
    path('stock_order/<pk>/', views.stock_order_detail, name='stock_order_detail'),   
    path('order/<pk>/', views.stock_order_detail, name='order_detail'),
    path('order_returns/<pk>/', views.order_return_detail, name='order_return_detail'),
    path('stock_order_returns/<pk>/', views.stock_order_return_detail, name='stock_order_return_detail'),
    path('', admin.site.urls),
]

