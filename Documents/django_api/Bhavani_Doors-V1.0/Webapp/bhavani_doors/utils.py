import datetime
from reportlab.platypus import Table, TableStyle, Paragraph,Spacer
from reportlab.lib.pagesizes import landscape, A4
from reportlab.lib.styles import getSampleStyleSheet,ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT
from reportlab.lib.units import mm
from reportlab.lib.fonts import addMapping
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import num2words
from .models import group_materials_item

pdfmetrics.registerFont(TTFont('DejaVu', 'DejaVuSans.ttf'))
pdfmetrics.registerFont(TTFont('DejaVu-Bold', 'DejaVuSans-Bold.ttf'))
addMapping('DejaVu', 0, 0, 'DejaVu')
addMapping('DejaVu', 1, 0, 'DejaVu-Bold')

def generate_gst_bill_pdf(order, items):
    
    elements = []
    # add image

    title_style = getSampleStyleSheet()['Title']
    title_style.fontSize = 10   
    title_style.alignment = 1
    title_style.leading = 12

    # Draw the sub heading
    sub_heading_style = getSampleStyleSheet()['Normal']
    sub_heading_style.fontName = 'DejaVu'
    sub_heading_style.fontSize = 8
    sub_heading_style.alignment = 1
    sub_heading_style.leading = 10

    sub_heading_style1 = getSampleStyleSheet()['Normal']
    sub_heading_style1.fontSize = 8
    sub_heading_style1.fontName = 'DejaVu'
    sub_heading_style1.alignment = 0
    sub_heading_style1.leading = 10

    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 8
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10

    box_style = TableStyle([
        ('BOX', (0, 0), (-1, 5), 0.5, colors.black),
        ('BOX', (1, 0), (-1, 5), 0.5, colors.black),
        ('GRID', (1, 0), (-1, -1), 0.5, colors.black),
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
    ])
    box_data = []
    box_data.append([Paragraph('Bhavani Doors', title_style),Paragraph('Invoice Bill', title_style)])
    box_data.append([Paragraph('SF No. 523/4, Elumathur Main Road,', sub_heading_style),Paragraph('GST No: 33ANZPT3929P1ZS', sub_heading_style2)])
    box_data.append([Paragraph('Solangapalayam,', sub_heading_style),Paragraph(f'Invoice No: {order.invoice_no}', sub_heading_style2)])
    box_data.append([Paragraph('Erode - 638153.', sub_heading_style),Paragraph(f'Date: {order.date.strftime("%d-%b-%Y")} ', sub_heading_style1)])    
    box_data.append([Paragraph('Email: mayilonpvc@gmailcom.', sub_heading_style),Paragraph('Dispatch Through: Self Transport', sub_heading_style1)])
    box_data.append([Paragraph('Phone: 9976071562', sub_heading_style),Paragraph(f'Vehicle No: {order.vehicle_no}', sub_heading_style1)])
    box = Table(box_data, colWidths=[122.6*mm, 56.7*mm],hAlign='LEFT', vAlign='TOP')
    box.setStyle(box_style)
    elements.append(box)

    box_data = []
    address = []
    address = order.address.splitlines()
    for i in range(6):
      if i < len(address):
        address[i] = address[i]
      else:
        address.append(' ')
    box_data.append([Paragraph(f'Bill To: {order.customer}', sub_heading_style2),Paragraph(f'Buyer GSTIN: {order.gst_no} ', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[0]}', sub_heading_style1),Paragraph(f'Delivery Date: {datetime.date.today().strftime("%d-%b-%Y")}', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[1]}', sub_heading_style1),Paragraph(f'Delivery Location: ', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[2]}', sub_heading_style1),Paragraph(f'Delivery Terms: ', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[3]}', sub_heading_style1),Spacer(1, 10)])
    box_data.append([Paragraph(f'{address[4]}', sub_heading_style1),Spacer(1, 10)])
    
    box = Table(box_data, colWidths=[122.6*mm, 56.7*mm],hAlign='LEFT', vAlign='TOP')
    box.setStyle(box_style)
    elements.append(box)

    # Adjust column width size to fit to page
    total_width = landscape(A4)[1] - 20*mm - 20*mm
    column_width = [total_width/11*0.6, total_width/9*2.5, total_width/9*1, total_width/9*1.5, total_width/9*1, total_width/9*1.5, total_width/9*1.5]

    # Draw the table
    data = [
        ['SNo', 'Item', 'HSN Code', 'Unit', 'Qty', 'Unit Price', 'Total Price'],
    ]

    for i, item in enumerate(items, start=1):
        data.append([i, item.material.name, item.material.hsncode, item.unit.name, str(item.quantity), f'₹ {item.unit_price}', f'₹ {item.total_price}'])
     
    table_style = TableStyle([
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('BOTTOM_BORDER', (0, 0), (-1, -1), 0.5, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), (0.9, 0.9, 0.9)),
        ('TEXT_COLOR', (0, 0), (-1, 0), colors.black),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 8),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('FONTNAME', (0, 1), (-1, -1), 'DejaVu'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('TEXT_COLOR', (0, -1), (-1, -1), colors.black),
        ('LINEBELOW', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEABOVE', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEBEFORE', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEAFTER', (0, 0), (-1, -1), 0.5, colors.black),
    ])

    table = Table(data, colWidths=column_width,hAlign='LEFT', vAlign='TOP')
    table.setStyle(table_style)
    elements.append(table)

    total_style = TableStyle([
        ('BOX', (0, 0), (-1, 2), 0.5, colors.black),
        ('BOX', (0, 3), (-1, -1), 0.5, colors.black),
        ('GRID', (1, 0), (-1, 4), 0.5, colors.black),
        ('GRID', (2, 0), (-1, 4), 0.5, colors.black),
        ('BOX',  (1, 5), (-1, -1), 0.5, colors.black),
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
    ])
    totalbox = []
    sub_total = round(sum([item.total_price for item in items]), 2)
    cgst = round(sub_total*(order.cgst/100), 2)
    sgst = round(sub_total*(order.sgst/100), 2)
    igst = round(sub_total*(order.igst/100), 2)
    total_amt = round(sub_total + cgst + sgst + igst,0)
    totalbox.append([Spacer(1, 10),Paragraph(f'Sub Total',sub_heading_style2),Paragraph(f'₹ {sub_total}',sub_heading_style1)])
    totalbox.append([Paragraph(f'Rupees(₹): {num2words.num2words(total_amt, lang='en_IN').capitalize()} only',sub_heading_style2),Paragraph(f'CSGT {order.cgst}%',sub_heading_style2),Paragraph(f'₹ {cgst}',sub_heading_style1)])
    totalbox.append([Spacer(1, 10),Paragraph(f'SGST {order.sgst}%',sub_heading_style2),Paragraph(f'₹ {sgst}',sub_heading_style1)])
    totalbox.append([Paragraph('Pay Account Details:',sub_heading_style2), Paragraph(f'IGST {order.igst}%',sub_heading_style2),Paragraph(f'₹ {igst}',sub_heading_style1)])
    totalbox.append([Paragraph('Bank Name: Karur Vysya Bank',sub_heading_style1),Paragraph('Total',sub_heading_style2),Paragraph(f'₹ {round(total_amt,2)}',sub_heading_style2)])
    totalbox.append([Paragraph('Account No: ***************',sub_heading_style1),Paragraph('Signature:',sub_heading_style2)])
    totalbox.append([Paragraph('IFSC Code: KVBL0001130',sub_heading_style1)])
    totalbox.append([Paragraph('Beneficiary Name: Bhavani Doors',sub_heading_style1)])
    totalbox = Table(totalbox, colWidths=[122.6*mm, 28.35*mm, 28.35*mm],hAlign='LEFT', vAlign='TOP')
    totalbox.setStyle(total_style)
    elements.append(totalbox)
    end_note = []
    end_note.append([Paragraph(f'System Generated Invoice:',sub_heading_style2)])
    elements.append(Table(end_note, colWidths=[122.6*mm],hAlign='CENTER', vAlign='TOP'))
    return elements

def generate_estimate_pdf(order, items):

    elements = []

    # Draw the title and sub heading inside a box
    title_style = getSampleStyleSheet()['Title']
    title_style.fontSize = 10   
    title_style.alignment = 1
    title_style.leading = 12

    # Draw the sub heading
    sub_heading_style = getSampleStyleSheet()['Normal']
    sub_heading_style.fontName = 'DejaVu'
    sub_heading_style.fontSize = 8
    sub_heading_style.alignment = 1
    sub_heading_style.leading = 10

    sub_heading_style1 = getSampleStyleSheet()['Normal']
    sub_heading_style1.fontSize = 8
    sub_heading_style1.fontName = 'DejaVu'
    sub_heading_style1.alignment = 0
    sub_heading_style1.leading = 10

    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 8
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10

    box_style = TableStyle([
        ('BOX', (0, 0), (-1, 5), 0.5, colors.black),
        ('BOX', (1, 0), (-1, -1), 0.5, colors.black),
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
    ])
    box_data = []
    box_data.append([Paragraph('Bhavani Doors', title_style)])
    box_data.append([Paragraph('SF No. 523/4, Elumathur Main Road,', sub_heading_style)])
    box_data.append([Paragraph('Solangaplayam,', sub_heading_style),Paragraph('Estimate', title_style)])
    box_data.append([Paragraph('Erode - 638153.', sub_heading_style)])    
    box_data.append([Paragraph('Email: mayilonpvc@gmailcom.', sub_heading_style)])
    box_data.append([Paragraph('Phone: 9976071562', sub_heading_style)])
    box = Table(box_data, colWidths=[122.6*mm, 56.7*mm],hAlign='LEFT', vAlign='TOP')
    box.setStyle(box_style)
    elements.append(box)

    box_data = []
    address = []
    address = order.customer.address.splitlines()
    for i in range(5):
      if i < len(address):
        address[i] = address[i]
      else:
        address.append(' ')
    box_data.append([Paragraph(f'Estimate To: {order.customer.name}', sub_heading_style2)])
    box_data.append([Paragraph(f'{address[0]}', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[1]}', sub_heading_style1),Paragraph(f'Date: {order.date.strftime("%d-%b-%Y")}', title_style)])
    box_data.append([Paragraph(f'{address[2]}', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[3]}', sub_heading_style1)])
    box_data.append([Paragraph(f'{address[4]}', sub_heading_style1)])
    
    box = Table(box_data, colWidths=[122.6*mm, 56.7*mm],hAlign='LEFT', vAlign='TOP')
    box.setStyle(box_style)
    elements.append(box)

    # Adjust column width size to fit to page
    total_width = A4[0] - 20*mm - 20*mm
    column_width = [total_width/11*0.6, total_width/9*3.5, total_width/9*1.5, total_width/9*1, total_width/9*1.5, total_width/9*1.5]

    # Draw the table
    data = [
        ['SNo', 'Item', 'Unit', 'Qty', 'Unit Price', 'Total Price'],
    ]

    for i, item in enumerate(items, start=1):
        data.append([i, f'{item.material.name} {item.material.design}', item.unit.name, str(item.quantity), f'₹ {item.unit_price}', f'₹ {item.total_price}'])
     
    table_style = TableStyle([
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('BOTTOM_BORDER', (0, 0), (-1, -1), 0.5, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), (0.9, 0.9, 0.9)),
        ('TEXT_COLOR', (0, 0), (-1, 0), colors.black),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 8),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('FONTNAME', (0, 1), (-1, -1), 'DejaVu'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('TEXT_COLOR', (0, -1), (-1, -1), colors.black),
        ('LINEBELOW', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEABOVE', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEBEFORE', (0, 0), (-1, -1), 0.5, colors.black),
        ('LINEAFTER', (0, 0), (-1, -1), 0.5, colors.black),
    ])

    table = Table(data, colWidths=column_width,hAlign='LEFT', vAlign='TOP')
    table.setStyle(table_style)

    elements.append(table)

    total_style = TableStyle([
        ('BOX', (0, 0), (-1, 2), 0.5, colors.black),
        ('BOX', (0, 3), (-1, -1), 0.5, colors.black),
        ('ALIGN', (1, 0), (-1, 4), 'RIGHT'),
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
    ])
    totalbox = []
    total = round(sum([item.total_price for item in items]), 2)
    total_with_freight = total + order.freight
    totalbox.append([Paragraph(f'Total Rupees(₹): {num2words.num2words(total_with_freight, lang='en_IN').capitalize()} only',sub_heading_style2),Paragraph(f'Sub Total',sub_heading_style2),Paragraph(f'₹ {total}',sub_heading_style2)])
    totalbox.append([Spacer(1, 10),Paragraph(f'Freight',sub_heading_style2),Paragraph(f'₹ {order.freight}',sub_heading_style2)])
    totalbox.append([Spacer(1, 10),Paragraph(f'Total',sub_heading_style2),Paragraph(f'₹ {total_with_freight}',sub_heading_style2)])
    totalbox = Table(totalbox, colWidths=[122.6*mm, 28.35*mm, 28.35*mm],hAlign='LEFT', vAlign='TOP')
    totalbox.setStyle(total_style)
    elements.append(totalbox)
    end_note = []
    end_note.append([Paragraph(f'System Generated Estimate Reference: {order.order_id}',sub_heading_style2)])
    elements.append(Table(end_note, colWidths=[122.6*mm],hAlign='CENTER', vAlign='TOP'))
    return elements

def generate_stock_order_pdf(order, items):

    elements = []

    # Draw the title and sub heading inside a box
    title_style = getSampleStyleSheet()['Title']
    title_style.fontSize = 10   
    title_style.alignment = 1
    title_style.leading = 12

    # Draw the sub heading
    sub_heading_style = getSampleStyleSheet()['Normal']
    sub_heading_style.fontName = 'DejaVu'
    sub_heading_style.fontSize = 8
    sub_heading_style.alignment = 1
    sub_heading_style.leading = 10

    sub_heading_style1 = getSampleStyleSheet()['Normal']
    sub_heading_style1.fontSize = 8
    sub_heading_style1.fontName = 'DejaVu'
    sub_heading_style1.alignment = 0
    sub_heading_style1.leading = 10

    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 8
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10

    box_style = TableStyle([
        ('BOX', (0, 0), (-1, 5), 0.5, colors.black),
        ('BOX', (1, 0), (-1, -1), 0.5, colors.black),
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
    ])
    box_data = []
    box_data.append([Paragraph('Bhavani Doors', title_style)])
    box_data.append([Paragraph('SF No. 523/4, Elumathur Main Road,', sub_heading_style)])
    box_data.append([Paragraph('Solangaplayam,', sub_heading_style),Paragraph(f'{order.supplier.name}',title_style)])
    box_data.append([Paragraph('Erode - 638153.', sub_heading_style), Paragraph(order.date.strftime('%d %b %Y'), title_style)])
    box_data.append([Paragraph('Email: mayilonpvc@gmailcom.', sub_heading_style)])
    box_data.append([Paragraph('Phone: 9976071562', sub_heading_style)])
    box = Table(box_data, colWidths=[122.6*mm, 56.7*mm],hAlign='LEFT', vAlign='TOP')
    box.setStyle(box_style)
    elements.append(box)
    elements.append(Spacer(1, 4*mm))

    # Capture material name and their quantity across all designs
    material_summary = {}

    for item in items:
        material_name = f"{item.material.name} ({item.unit.name})"
        if item.material.sales_type == 'group':
            group_materials = group_materials_item.objects.filter(group=item.material)
            for group_material in group_materials:
                material_name = f"{group_material.item.name} ({group_material.item.unit.name})"
                if material_name not in material_summary:
                    material_summary[material_name] = 0
                material_summary[material_name] += round(group_material.qnty * item.quantity, 2)
        else:
            if material_name not in material_summary:
                material_summary[material_name] = 0
            material_summary[material_name] += round(item.quantity, 2)

    # Draw separate tables for each unique design name
    data_tables = []
    design_material_mapping = {}
    for item in items:
        design_name = item.material.design.name
        design_color_key = (item.material.design.name, item.material.design.color)
        if design_name not in design_material_mapping:
            design_material_mapping[design_name] = {}
        if design_color_key not in design_material_mapping[design_name]:
            design_material_mapping[design_name][design_color_key] = {}
        if item.material.sales_type == 'group':
            group_materials = group_materials_item.objects.filter(group=item.material)
            for group_material in group_materials:
                material_name = f"{group_material.item.name} ({group_material.item.unit.name})"
                if material_name not in design_material_mapping[design_name][design_color_key]:
                    design_material_mapping[design_name][design_color_key][material_name] = 0
                design_material_mapping[design_name][design_color_key][material_name] += round(group_material.qnty * item.quantity, 2)
        else:
            material_name = f"{item.material.name} ({item.unit.name})"
            if material_name not in design_material_mapping[design_name][design_color_key]:
                design_material_mapping[design_name][design_color_key][material_name] = 0
            design_material_mapping[design_name][design_color_key][material_name] += round(item.quantity, 2)

    for design_name, color_mapping in design_material_mapping.items():
        header = [design_name] + sorted(list(set([material_name for color_dict in color_mapping.values() for material_name in color_dict.keys()])), key=lambda x: (x.lower().find('panel'), x.lower().find('sec'), x.lower().find('frame')), reverse=True)
        data = [header]

        for i, ((design_name_key, design_color), materials_dict) in enumerate(color_mapping.items()):
            row = [design_color] + [str(materials_dict.get(material_name, 0)) for material_name in header[1:]]
            data.append(row)

        # Calculate column widths based on maximum content length
        widths = [85 for _ in range(len(data[0]))]

        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f2f2f2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#333333')),
            ('ALIGN', (0, 0), (-1, 0), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 2),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#333333')),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#cccccc')),
            ('TOPPADDING', (0, 0), (-1, -1), 1),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 1),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
        ])

        table = Table(data, colWidths=widths, hAlign='LEFT', vAlign='TOP')
        table.setStyle(table_style)

        data_tables.append(table)

    for table in data_tables:
        elements.append(table)

    elements.append(Spacer(1, 3 * mm))
    # Create summary table for unique material names and their total quantities
    summary_header = ['Material Name', 'Total Quantity']
    summary_data = [summary_header] + [[material, str(round(qnty, 2))] for material, qnty in sorted(material_summary.items(), key=lambda x: (x[0].lower().find('panel'), x[0].lower().find('sec'), x[0].lower().find('frame')), reverse=True)]

    summary_table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f2f2f2')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#333333')),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 8),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#333333')),
        ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#cccccc')),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
    ])

    summary_table = Table(summary_data, colWidths=[100, 100], hAlign='LEFT', vAlign='TOP')
    summary_table.setStyle(summary_table_style)
    elements.append(summary_table)
    elements.append(Spacer(1, 4 * mm))

    end_note = []
    end_note.append([Paragraph(f'System Generated Order Reference: {order.order_id}', sub_heading_style2)])
    elements.append(Table(end_note, colWidths=[122.6 * mm], hAlign='CENTER', vAlign='TOP'))
    return elements    


def generate_customer_balance_list_pdf(ordered_customers):
    elements = []
    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 8
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10
    balance_list_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f2f2f2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#333333')),
            ('ALIGN', (0, 0), (-1, 0), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 2),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#333333')),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#cccccc')),
            ('TOPPADDING', (0, 0), (-1, -1), 1),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 1),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f2f2f2')),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-BoldOblique'),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#333333')),
        ])
    
    customer_types = {}
    total_summary = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    
    for customer in ordered_customers:
        if customer.remaining_amount_init < 0:
            customer_type = customer.type
            if customer_type not in customer_types:
                customer_types[customer_type] = []
            total_opening_amt = customer.opening_amt    
            total_order_amt = customer.total_order_amt
            total_freight = customer.total_freight
            total_order_return_amt = customer.total_order_return_amt
            percentage_of_return = round((total_order_return_amt / total_order_amt * 100), 2) if total_order_amt else 0
            total_payment = customer.total_payment
            percentage_of_payment = round(((total_payment + total_opening_amt + total_order_return_amt + customer.total_discount) / total_order_amt * 100), 2) if total_order_amt else 0
            # Truncate customer name if longer than 25 characters
            display_name = customer.name[:25] + '..' if len(customer.name) > 25 else customer.name
            customer_types[customer_type].append([
                len(customer_types[customer_type]) + 1,
                display_name,
                customer.opening_amt,
                customer.total_order_count,
                total_order_amt,
                total_freight,
                total_order_return_amt,
                percentage_of_return,
                customer.total_discount,
                total_payment,
                percentage_of_payment,
                customer.remaining_amount_init
            ])
            total_summary[0] += 1
            total_summary[2] += customer.opening_amt
            total_summary[3] += customer.total_order_count
            total_summary[4] += total_order_amt
            total_summary[5] += total_freight
            total_summary[6] += total_order_return_amt
            total_summary[8] += customer.total_discount
            total_summary[9] += total_payment
            total_summary[11] += customer.remaining_amount_init

    for customer_type, data in customer_types.items():
        elements.append(Paragraph(f'Customer Type: {customer_type}', sub_heading_style2))
        elements.append(Spacer(1, 4*mm))
        data.insert(0, ['#', 'Customer Name', 'Opng Amt', 'TotOrdCnt', 'TotOrdAmt', 'TotFrght', 'TotRetAmt', 'TOR%', 'TotDisc', 'TotPay', 'Totpaid%', 'TotRemAmt'])
        data[1:] = sorted(data[1:], key=lambda x: x[11])  # Sort by remaining amount
        for i, row in enumerate(data[1:]):
            row[0] = i + 1
        total_row = [
            '',
            f'Total - {customer_type}',
            sum(row[2] for row in data[1:]),
            sum(row[3] for row in data[1:]),
            sum(row[4] for row in data[1:]),
            sum(row[5] for row in data[1:]),
            sum(row[6] for row in data[1:]),
            round((sum(row[6] for row in data[1:]) / sum(row[4] for row in data[1:])) * 100, 2) if sum(row[4] for row in data[1:]) else 0,
            sum(row[8] for row in data[1:]),
            sum(row[9] for row in data[1:]),
            round(((sum(row[9] for row in data[1:]) + sum(row[2] for row in data[1:]) + sum(row[6] for row in data[1:]) + sum(row[8] for row in data[1:])) / sum(row[4] for row in data[1:])) * 100, 2) if sum(row[4] for row in data[1:]) else 0,
            sum(row[11] for row in data[1:])
        ]
        data.append(total_row)
        table = Table(data, colWidths=[5*mm, 40*mm, 15*mm, 15*mm, 20*mm, 12*mm, 20*mm, 10*mm, 13*mm, 20*mm, 15*mm, 20*mm], hAlign='LEFT', vAlign='TOP')
        table.setStyle(balance_list_style)
        elements.append(table)
        elements.append(Spacer(1, 4*mm))
    total_summary_row = [
        '',
        'Total',
        total_summary[2],
        total_summary[3],
        total_summary[4],
        total_summary[5],
        total_summary[6],
        round((total_summary[6] / total_summary[4]) * 100, 2) if total_summary[4] else 0,
        total_summary[8],
        total_summary[9],
        round(((total_summary[9] + total_summary[2] + total_summary[6] + total_summary[8]) / total_summary[4]) * 100, 2) if total_summary[4] else 0,
        total_summary[11]
    ]
    total_summary_table = Table([total_summary_row], colWidths=[5*mm, 40*mm, 15*mm, 15*mm, 20*mm, 12*mm, 20*mm, 10*mm, 13*mm, 20*mm, 15*mm, 20*mm], hAlign='LEFT', vAlign='TOP')
    total_summary_table.setStyle(balance_list_style)
    elements.append(total_summary_table)
    
    return elements

def generate_material_statement_pdf(item_no,stmt_material,order_items,stock_items,order_return_items,stock_return_items):

    link_style = ParagraphStyle(name='Link', fontSize=8, textColor=colors.blue, 
                            underlineColor=colors.blue, underlineWidth=1, 
                            fontName='Helvetica', alignment=TA_LEFT)    
    
    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 8
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10
    material_statement_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f2f2f2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#333333')),
            ('ALIGN', (0, 0), (-1, 0), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 2),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#333333')),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#cccccc')),
            ('TOPPADDING', (0, 0), (-1, -1), 1),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 1),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
            ('FONTNAME', (-1, -1), (-1, -1), 'Helvetica-BoldOblique'),
        ])
    today = datetime.datetime.now()
    material_statement = []
    data_rows = []
    data = []    
    total_qnty = 0
    sub_total = 0
    material_statement.append(Spacer(1, 10))
    material_statement.append(Paragraph(f'{item_no} - Statement for {stmt_material.name} - {stmt_material.design} - As of Date & Time: {today.strftime("%d-%b-%Y %H:%M:%S")}', sub_heading_style2))
    material_statement.append(Spacer(1, 10))
    total_qnty += stmt_material.opening_quantity * stmt_material.unit.qnty
    for i, item in enumerate(stock_items, start=1):
        data_rows.append([str(i), Paragraph(f'<a href="{item.order.get_absolute_url()}">{item.order.supplier.name} {item.order.order_id}</a>', link_style), 
                  item.order.date.strftime('%d-%b-%Y'), f'{str(item.quantity)}', f'{item.unit.name}', 
                  f'{item.unit_price:.2f}', f'{item.total_price:.2f}'])     
        total_qnty += item.quantity * item.unit.qnty
        sub_total += item.quantity * item.unit.qnty
    data.append([f'S.No', 'Stocks', 'Date', 'Qnty', 'Unit', 'Unit Price', 'Total Price'])
    data.append(['0', f'Opening Balance', f'', f'{stmt_material.opening_quantity}', f'{stmt_material.unit.name}', f'{stmt_material.opening_unit_stock_price:.2f}', f'{stmt_material.opening_unit_stock_price * stmt_material.opening_quantity:.2f}'])                
    data.extend(data_rows)
    if sub_total > 0:
        data.append(['','Total', '', f'{str(sub_total/stmt_material.unit.qnty)}', f'{stmt_material.unit.name}', ''])
    else:
        data.append(['','Total', '', f'0.00', f'{stmt_material.unit.name}', ''])           
    total_width = A4[0] - 20*mm - 20*mm
    material_statement_table = Table(data, colWidths=[10*mm, total_width*0.45, total_width*0.15, total_width*0.1, total_width*0.15, total_width*0.13, total_width*0.13],hAlign='LEFT', vAlign='TOP')
    material_statement_table.setStyle(material_statement_style)
    material_statement.append(material_statement_table)                 
    sub_total = 0
    data = []
    data_rows = []    
    for i, item in enumerate(stock_return_items, start=1):
        data_rows.append([str(i), Paragraph(f'<a href="{item.get_absolute_url()}"> {item.supplier.name} {item.sno}</a>', link_style)])   
        total_qnty -= item.quantity * item.unit.qnty
        sub_total += item.quantity * item.unit.qnty
    if sub_total > 0:        
        data.append([f'S.No', 'Stock Returns', 'Date', 'Qnty', 'Unit', 'Unit Price', 'Total Price'])
        data.extend(data_rows)
        data.append(['','Total', '', f'{str(sub_total/stmt_material.unit.qnty)}', f'{stmt_material.unit.name}', ''])
        total_width = A4[0] - 20*mm - 20*mm
        material_statement_table = Table(data, colWidths=[10*mm, total_width*0.45, total_width*0.15, total_width*0.1, total_width*0.15, total_width*0.13, total_width*0.13],hAlign='LEFT', vAlign='TOP')
        material_statement_table.setStyle(material_statement_style)
        material_statement.append(material_statement_table)        
    else:
        pass 
    sub_total = 0    
    data = []  
    data_rows = []          
    for i, item in enumerate(order_items, start=1):
        data_rows.append([str(i), Paragraph(f'<a href="{item.order.get_absolute_url()}">{item.order.customer.name} {item.order.order_id}</a>', link_style), 
                  item.order.date.strftime('%d-%b-%Y'), f'{str(item.quantity)}', f'{item.unit.name}', 
                  f'{item.unit_price:.2f}', f'{item.total_price:.2f}']) 
        total_qnty -= item.quantity * item.unit.qnty
        sub_total += item.quantity * item.unit.qnty
    if sub_total > 0:    
        data.append([f'S.No', 'Orders', 'Date', 'Qnty', 'Unit', 'Unit Price', 'Total Price'])
        data.extend(data_rows)
        data.append(['','Total', '', f'{str(sub_total/stmt_material.unit.qnty)}', f'{stmt_material.unit.name}', ''])
        total_width = A4[0] - 20*mm - 20*mm
        material_statement_table = Table(data, colWidths=[10*mm, total_width*0.45, total_width*0.15, total_width*0.1, total_width*0.15, total_width*0.13, total_width*0.13],hAlign='LEFT', vAlign='TOP')
        material_statement_table.setStyle(material_statement_style)
        material_statement.append(material_statement_table)         
    else:
        pass 
    sub_total = 0  
    data = []  
    data_rows = []            
    for i, item in enumerate(order_return_items, start=1):
        data_rows.append([str(i), Paragraph(f'<a href="{item.get_absolute_url()}"> {item.customer.name} {item.sno}</a>', link_style),
                  item.date.strftime('%d-%b-%Y'), f'{str(item.quantity)}', f'{item.unit.name}', 
                  f'{item.unit_price:.2f}', f'{item.total_price:.2f}'])
        total_qnty += item.quantity * item.unit.qnty     
        sub_total += item.quantity * item.unit.qnty  
    if sub_total > 0:         
        data.append([f'S.No', 'Order Returns', 'Date', 'Qnty', 'Unit', 'Unit Price', 'Total Price'])
        data.extend(data_rows)
        data.append(['','Total', '', f'{str(sub_total/stmt_material.unit.qnty)}', f'{stmt_material.unit.name}', ''])
        total_width = A4[0] - 20*mm - 20*mm
        material_statement_table = Table(data, colWidths=[10*mm, total_width*0.45, total_width*0.15, total_width*0.1, total_width*0.15, total_width*0.13, total_width*0.13],hAlign='LEFT', vAlign='TOP')
        material_statement_table.setStyle(material_statement_style)
        material_statement.append(material_statement_table)        
    else:
        pass    
    sub_total = 0
    data = []
    data_rows = []
    data.append(['','Closing Stock Balance', '', str(total_qnty/stmt_material.unit.qnty), f'{stmt_material.unit.name}', f'{stmt_material.stock_unit_price:.2f}', f'{stmt_material.total_stock_value:.2f}'])
    total_width = A4[0] - 20*mm - 20*mm
    material_statement_table = Table(data, colWidths=[10*mm, total_width*0.45, total_width*0.15, total_width*0.1, total_width*0.15, total_width*0.13, total_width*0.13],hAlign='LEFT', vAlign='TOP')
    material_statement_table.setStyle(material_statement_style)
    material_statement.append(material_statement_table)    
    return material_statement

def generate_customer_statement_pdf(item_no, customer, orders, order_return_items, customer_payments):

    link_style = ParagraphStyle(name='Link', fontSize=7, textColor=colors.blue, 
                                underlineColor=colors.blue, underlineWidth=1, 
                                fontName='Helvetica', alignment=TA_LEFT)    
    
    sub_heading_style2 = getSampleStyleSheet()['Normal']
    sub_heading_style2.fontSize = 7
    sub_heading_style2.fontName = 'DejaVu-Bold'
    sub_heading_style2.alignment = 0
    sub_heading_style2.leading = 10
    customer_statement_style = TableStyle([
        ('TOPPADDING', (0, 0), (-1, -1), 0.5),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0.5),
        ('FONTNAME', (0, 0), (-1, 0), 'DejaVu-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 7),
        ('FONTNAME', (0, 1), (-1, -1), 'DejaVu'),
        ('FONTSIZE', (0, 1), (-1, -1), 7),
        ('FONTNAME', (0, -1), (-1, -1), 'DejaVu-Bold'),
        ('FONTSIZE', (0, -1), (-1, -1), 7),
        ('GRID', (0, 0), (-1, -2), 0.5, colors.black),
        ('BOX', (0, 0), (-1, -1), 0.5, colors.black),
    ])
    today = datetime.datetime.now()
    customer_statement = []
    customer_statement.append(Spacer(1, 10))
    customer_statement.append(Paragraph(f'{item_no} - Statement for {customer.name} - As of Date & Time: {today.strftime("%d-%b-%Y %H:%M:%S")}', sub_heading_style2))

    def create_table(data, col_widths):
        table = Table(data, colWidths=col_widths, hAlign='LEFT', vAlign='TOP')
        table.setStyle(customer_statement_style)
        return table

    total_width = A4[0] - 20*mm - 20*mm
    col_widths = [10*mm, total_width*0.3, total_width*0.3, total_width*0.4]

    data = [['', 'Opening Balance', 'Debt', 'Paid Extra']]
    data.append(['', f'{customer.opening_amt:.2f}' if customer.opening_amt < 0 else '', f'{abs(customer.opening_amt):.2f}'])
    customer_statement.append(create_table(data, col_widths))

    sections = [
        ('Order', orders, lambda x: [f'{x.order_id}', f'{x.date.strftime("%d-%b-%Y")}', f'{x.total_amt:.2f}']),
        ('Order Return', order_return_items, lambda x: [f'{x.sno} - {x.material.name} - {x.material.design.name}', f'{x.date.strftime("%d-%b-%Y")}', f'{x.total_price:.2f}']),
        ('Payment', customer_payments, lambda x: [f'ID - {x.id} - {x.trans_type} {x.ref_no if x.ref_no is not None else ""}', f'{x.date.strftime("%d-%b-%Y")}', f'{x.amount:.2f}']),
    ]

    total = 0

    for title, items, row_func in sections:
        data = [['S.No', title, 'Date', 'Amount']]
        data_rows = []
        sub_total = 0
        for sno, item in enumerate(items, start=1):
            sub_total += getattr(item, 'total_price', getattr(item, 'total_amt', getattr(item, 'amount', 0)))
            data_rows.append([f'{sno}'] + row_func(item))
        total += sub_total if title == 'Order' else -sub_total
        data.extend(data_rows)
        data.append(['', 'Total', '', f'{sub_total:.2f}'])
        customer_statement.append(create_table(data, col_widths))

    data = [['', 'Total Discount', '', f'{customer.total_discount:.2f}']]
    customer_statement.append(create_table(data, col_widths))

    data = [['', 'Closing Balance', '', f'{abs(total - customer.total_discount):.2f} (Paid Extra)' if total - customer.total_discount < 0 else f'{total - customer.total_discount:.2f} (Debt)']]
    customer_statement.append(create_table(data, col_widths))

    return customer_statement

