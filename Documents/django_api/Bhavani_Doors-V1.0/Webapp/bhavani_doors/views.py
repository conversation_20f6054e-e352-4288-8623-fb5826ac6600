from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from .models import material, order, stock_order,unit

def get_material_order_price(request):
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Not Authorized'}, status=401)
    material_id = request.GET.get('material_id')
    unit_id = request.GET.get('unit_id')
    units = unit.objects.get(id=unit_id)
    materials = material.objects.get(id=material_id)
    nos_price = materials.unit_order_price/materials.unit.qnty
    return JsonResponse({'unit_order_price': round(nos_price * units.qnty,2)})

def get_material_unit(request):
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Not Authorized'}, status=401)
    material_id = request.GET.get('material_id')
    materials = material.objects.get(id=material_id)
    return JsonResponse({'unitid': materials.unit.id})


def stock_order_detail(request, pk):
    obj = get_object_or_404(stock_order, pk=pk)
    return render(request, 'stock_order_detail.html', {'stock_order': obj})

def order_detail(request, pk):
    obj = get_object_or_404(order, pk=pk)
    return render(request, 'order_detail.html', {'order': obj})

def order_return_detail(request, pk):
    obj = get_object_or_404(order, pk=pk)
    return render(request, 'order_return_detail.html', {'order': obj})

def stock_order_return_detail(request, pk):
    obj = get_object_or_404(stock_order, pk=pk)
    return render(request, 'stock_order_return_detail.html', {'stock_order': obj})
