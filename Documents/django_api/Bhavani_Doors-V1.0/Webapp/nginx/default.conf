log_format custom '$remote_addr - $http_cf_connecting_ip - $http_cf_ipcountry - $http_cf_access_authenticated_user_email  - [$time_local] '
                '"$request" $status $body_bytes_sent '
                '"$http_referer" "$http_user_agent" $request_time $upstream_response_time';

upstream web {
    server app:8003;
    keepalive 32;
}

server {
    listen  443 ssl default_server;
    server_name _; # This is a catch-all server name
    # This is the server SSL certificate
    ssl_certificate      /run/secrets/CERTS/servercert.pem;
    # This is the server certificate key
    ssl_certificate_key /run/secrets/CERTS/serverkey.pem;    
    return 444;
}

server {
    # Listen on port 443 for HTTPS connections
    listen  443 ssl http2;
    
    # Name of the server/website
    server_name admin.bhavanihomedecors.co.in;

    # This is the server SSL certificate
    ssl_certificate      /run/secrets/CERTS/servercertcloud.pem;

    # This is the server certificate key
    ssl_certificate_key /run/secrets/CERTS/serverkeycloud.pem;

    ssl_client_certificate /run/secrets/CERTS/usercertcloud.pem;

    # Enables mutual TLS/two way SSL to verify the client
    #cloud is not sending cert so disbled - need to review later
    ssl_verify_client off;

    # Number of intermediate certificates to verify.
    ssl_verify_depth 1;

    proxy_ssl_server_name on;
    access_log /var/log/nginx/access.log custom;
    # Any error during the connection can be found on the following path
    error_log /var/log/nginx/error.log;
    keepalive_timeout 65;
    ssl_session_timeout 5m;

    # Timeout settings to prevent upstream timeout errors
    proxy_connect_timeout 120s;
    proxy_send_timeout 120s;
    proxy_read_timeout 120s;

    ## Compression
    gzip              on;
    gzip_buffers      16 8k;
    gzip_comp_level   4;
    gzip_http_version 1.0;
    gzip_min_length   1280;
    gzip_types        text/plain text/css application/x-javascript text/xml application/xml application/xml+rss text/javascript image/x-icon image/bmp;
    gzip_vary         on;

    location / {
        proxy_pass http://web;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header X-UID $http_x_uid;
        proxy_redirect off;
        
        # Add timeout settings in location block as well
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }  

    location /static/ {
        alias /static/;
    }

    location /favicon.ico {
        alias /static/favicon.ico;
    }  
}
