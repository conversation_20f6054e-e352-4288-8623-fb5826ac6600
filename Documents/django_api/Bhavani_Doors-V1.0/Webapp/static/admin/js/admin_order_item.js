$(document).ready(function() {
    var materialSelect = django.jQuery('#id_material');
    var unitSelect = django.jQuery('#id_unit');
    var unitPriceInput = django.jQuery('#id_unit_price');

    if (!materialSelect.length || !unitSelect.length || !unitPriceInput.length) {
        console.error('Required DOM elements not found');
        return;
    };

    var handleSelectChangematerial = function() {
        var material_id = materialSelect.val();
        if (material_id !== null) {
            django.jQuery.get('/autofillprice/get_material_unit/', {material_id: material_id}, function(data) {
                unitSelect.val(data.unitid);
                unitSelect.trigger('change'); 
            })
        };
    };
    var handleSelectChangeunit = function() {
        var material_id = materialSelect.val();
        var unit_id = unitSelect.val();
        if (material_id !== null && (unit_id === null || unit_id === "")) {
            django.jQuery.get('/autofillprice/get_material_unit/', {material_id: material_id}, function(data) {
                unitSelect.val(data.unitid)
                unit_id = data.unitid
            })
        }
        if (material_id !== null && unit_id !== "") {
            django.jQuery.get('/autofillprice/get_material_order_price/', {material_id: material_id, unit_id: unit_id}, function(data) {
                unitPriceInput.val(data.unit_order_price);
            });
        }
    };

    materialSelect.on('change', handleSelectChangematerial);
    unitSelect.on('change', handleSelectChangeunit);
});