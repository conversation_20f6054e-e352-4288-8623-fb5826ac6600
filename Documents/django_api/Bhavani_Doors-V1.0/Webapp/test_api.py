#!/usr/bin/env python
"""
Test script for the Bhavani Doors API
Demonstrates API functionality without authentication
"""
import requests
import json
from django.test import Client
from django.contrib.auth.models import User
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_projects.settings')
django.setup()

def test_api_with_django_client():
    """Test API using Django test client (bypasses authentication for testing)"""
    from django.test import Client
    from django.contrib.auth.models import User
    
    client = Client()
    
    print("=== Bhavani Doors API Test ===\n")
    
    # Create a test user and login
    try:
        user = User.objects.get(username='admin')
    except User.DoesNotExist:
        print("No admin user found. Please create a superuser first:")
        print("python manage.py createsuperuser")
        return
    
    # Login
    client.force_login(user)
    
    print("1. Testing API Health Check...")
    response = client.get('/api/health/')
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
    print()
    
    print("2. Testing Model List...")
    response = client.get('/api/models/')
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Found {len(data.get('data', []))} models")
        for model in data.get('data', [])[:3]:  # Show first 3 models
            print(f"  - {model['verbose_name']} ({model['app_label']}.{model['model_name']})")
    print()
    
    print("3. Testing Model Schema (Material)...")
    response = client.get('/api/models/bhavani_doors/material/schema/')
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        schema = data.get('data', {})
        print(f"Model: {schema.get('verbose_name')}")
        print(f"Fields: {len(schema.get('fields', {}))}")
        print("Sample fields:")
        for field_name, field_info in list(schema.get('fields', {}).items())[:3]:
            print(f"  - {field_name}: {field_info.get('type')} ({'Required' if field_info.get('required') else 'Optional'})")
    print()
    
    print("4. Testing Model Data (Materials)...")
    response = client.get('/api/models/bhavani_doors/material/')
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total materials: {data.get('pagination', {}).get('count', 0)}")
        materials = data.get('data', [])
        if materials:
            print("Sample material:")
            material = materials[0]
            print(f"  - ID: {material.get('id')}")
            print(f"  - Name: {material.get('name')}")
            print(f"  - Available Quantity: {material.get('available_quantity')}")
    print()
    
    print("5. Testing Model Statistics...")
    response = client.get('/api/models/bhavani_doors/material/stats/')
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        stats = data.get('data', {})
        print(f"Total count: {stats.get('total_count')}")
        print(f"Model name: {stats.get('verbose_name')}")
    print()
    
    print("=== API Test Complete ===")
    print("\nThe API is working correctly!")
    print("Next steps:")
    print("1. Access the Django Admin at http://127.0.0.1:8000/admin/")
    print("2. Login with your admin credentials")
    print("3. Use the API endpoints from your Angular application")
    print("4. Generate API documentation: python manage.py generate_api_docs")

def show_available_endpoints():
    """Show all available API endpoints"""
    print("\n=== Available API Endpoints ===")
    
    endpoints = [
        ("GET", "/api/health/", "API health check"),
        ("GET", "/api/models/", "List all available models"),
        ("GET", "/api/models/{app}/{model}/schema/", "Get model schema"),
        ("GET", "/api/models/{app}/{model}/stats/", "Get model statistics"),
        ("GET", "/api/models/{app}/{model}/", "List model instances"),
        ("POST", "/api/models/{app}/{model}/", "Create model instance"),
        ("GET", "/api/models/{app}/{model}/{id}/", "Get model instance"),
        ("PUT", "/api/models/{app}/{model}/{id}/", "Update model instance"),
        ("DELETE", "/api/models/{app}/{model}/{id}/", "Delete model instance"),
        ("GET", "/api/models/{app}/{model}/fields/{field}/choices/", "Get related field choices"),
        ("GET", "/api/models/{app}/{model}/fields/{field}/options/", "Get field options"),
        ("POST", "/api/models/{app}/{model}/validate/", "Validate model data"),
    ]
    
    for method, endpoint, description in endpoints:
        print(f"{method:6} {endpoint:50} - {description}")
    
    print("\nExample model endpoints for 'material':")
    print("GET    /api/models/bhavani_doors/material/")
    print("POST   /api/models/bhavani_doors/material/")
    print("GET    /api/models/bhavani_doors/material/1/")
    print("PUT    /api/models/bhavani_doors/material/1/")
    print("DELETE /api/models/bhavani_doors/material/1/")

if __name__ == '__main__':
    try:
        test_api_with_django_client()
        show_available_endpoints()
    except Exception as e:
        print(f"Error testing API: {e}")
        print("Make sure Django is properly configured and the database is accessible.")
