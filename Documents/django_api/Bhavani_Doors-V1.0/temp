
%Bgg?q^67qm^hi8

$6k.MCxfY7Bg7Ak


mysql -u bhavanidoors -h bhavanidoors.mysql.pythonanywhere-services.com 'bhavanidoors$default'  < Dump20240921.sql



URL	Directory	Delete
/static/	/home/<USER>/static/	 
/favicon.ico	/home/<USER>/static/favicon.ico	 
Enter URL	Enter path

/home/<USER>/.virtualenvs/myvirtualenv


/home/<USER>/Bhavani_Doors/Webapp







upstream web {	
    server 0.0.0.0:8000;
}
server {
    listen  443 ssl default_server;
    server_name _; # This is a catch-all server name
    # This is the server SSL certificate
    ssl_certificate      /etc/nginx/certs/servercert.pem;
    # This is the server certificate key
    ssl_certificate_key /etc/nginx/certs/serverkey.pem;    
    return 444;
}
            log_format custom '$remote_addr - $http_cf_connecting_ip - $http_cf_ipcountry - $http_x_uid - [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" $request_time $upstream_response_time';

server {
    # Listen on port 443 for HTTPS connections
    listen  443 ssl;
    
    # This is the server SSL certificate
    ssl_certificate      /etc/nginx/certs/servercertcloud.pem;

    # This is the server certificate key
    ssl_certificate_key /etc/nginx/certs/serverkeycloud.pem;

    ssl_client_certificate /etc/nginx/certs/usercertcloud.pem;

    # Enables mutual TLS/two way SSL to verify the client
    ssl_verify_client on;

    # Number of intermediate certificates to verify. Good explanation of 
    # certificate chaining can be found at
    # https://cheapsslsecurity.com/p/what-is-ssl-certificate-chain/
    ssl_verify_depth 1;
  
  
   
    # Name of the server/website
    server_name admin.bhavanihomedecors.co.in;

    proxy_ssl_server_name on;
    access_log /var/log/nginx/access.log custom;
    # Any error during the connection can be found on the following path
    error_log /var/log/nginx/error.log;
    keepalive_timeout 10;
    ssl_session_timeout 5m;

    location / {
	proxy_pass http://web;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	proxy_set_header Host $host;
	proxy_set_header X-UID $http_x_uid;
	proxy_redirect off;
    }  
    
    location /static/ {
    alias /var/www/static/;
    }
    location /favicon.ico {
    alias /var/www/static/favicon.ico;
    }
}


Vs]6C0;0un;

bvgoat
bvgoat@321

CREATE DATABASE bvdoors

CREATE USER 'bvgoat'@'localhost' IDENTIFIED BY 'Pass@go3';

mysql -u bvgoat -p bvdoors < latest.sql

sudo apt-get install python3-dev




GRANT ALL PRIVILEGEs ON bvdoors.* TO 'bvgoat'@'localhost' WITH GRANT OPTION;
			

sudo ufw allow from ************/20  to any port 443 proto tcp;
sudo ufw allow from ************/22 to any port 443 proto tcp;
sudo ufw allow from ************/22 to any port 443 proto tcp;
sudo ufw allow from **********/22 to any port 443 proto tcp;
sudo ufw allow from ************/18 to any port 443 proto tcp;
sudo ufw allow from *************/18 to any port 443 proto tcp;
sudo ufw allow from ************/20 to any port 443 proto tcp;
sudo ufw allow from ************/20 to any port 443 proto tcp;
sudo ufw allow from *************/22 to any port 443 proto tcp;
sudo ufw allow from ************/17 to any port 443 proto tcp;
sudo ufw allow from ***********/15 to any port 443 proto tcp;
sudo ufw allow from **********/13 to any port 443 proto tcp;
sudo ufw allow from **********/14 to any port 443 proto tcp;
sudo ufw allow from **********/13 to any port 443 proto tcp;
sudo ufw allow from **********/22 to any port 443 proto tcp;
sudo ufw allow from 2400:cb00::/32  to any port 443 proto tcp;
sudo ufw allow from 2606:4700::/32  to any port 443 proto tcp;
sudo ufw allow from 2803:f800::/32  to any port 443 proto tcp;
sudo ufw allow from 2405:b500::/32  to any port 443 proto tcp;
sudo ufw allow from 2405:8100::/32  to any port 443 proto tcp;
sudo ufw allow from 2a06:98c0::/29  to any port 443 proto tcp;
sudo ufw allow from 2c0f:f248::/32  to any port 443 proto tcp;


openssl pkcs12 -export -out keyStore.p12 -inkey userkeycloud.pem -in usercertcloud.pem -legacy


sudo netstat -pnt - to see list of service connecting network


sudo systemctl disable systemd-resolved.service

sudo systemctl stop systemd-resolved
sudo systemctl restart systemd-resolved


sudo ln -s /run/systemd/resolve/resolv.conf /etc/resolv.conf


sudo systemctl restart nginx


sudo ufw default allow outgoing
gunicorn web_projects.wsgi --bind 0.0.0.0:8000
gunicorn  --bind 0.0.0.0:8000 web_projects.wsgi

netstat -tlnp
sudo netstat -pnt

sudo /etc/init.d/mysql start

sudo less /var/log/ufw.log


install all req verions
pip install --upgrade -r requirements.txt

Python Upgrade
pip-review --local
pip-review --auto

pip freeze > requirements.txt


sudo powertop --auto-tune





    gst_invoice_no = models.PositiveSmallIntegerField(blank=True, null=True, default=0)
    gst_no = models.CharField(max_length=15, blank=True, null=True)
    gst_vehicle_no = models.CharField(max_length=15, blank=True, null=True)
    gst_tax = models.ForeignKey(gst_tax, on_delete=models.RESTRICT,editable=False)
    gst_address = models.TextField(blank=True, null=True)


            self.gst_tax = gst_tax.objects.first()      
        if self.gst_invoice_no == 0:
            gst_item = gst_tax.objects.first()
            self.gst_invoice_no = gst_item.next_invoice_no  
            super().save(*args, **kwargs)
            gst_item.next_invoice_no = gst_item.next_invoice_no + 1
            gst_item.save()

generate_gst_bill   


    def generate_gst_bill(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=20, leftMargin=20,
                                topMargin=20, bottomMargin=20)

        elements = []

        for order in queryset:
            # Generate PDF content for each order
            order_pdf_content = generate_gst_bill_pdf(order, order_item.objects.filter(order_id=order.order_id))
            elements.extend(order_pdf_content)
            elements.append(PageBreak())  # Add a page break between orders
        doc.build(elements)

        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="order_bills.pdf"'
        return response

        'gst_unit_price'

    hsncode = models.CharField(max_length=10)        


    mysqldump -u root -p bhavani_doors_prod > bhavani_prod_backup.sql

    docker compose --env-file ../../secrets/.env up -d

    sudo systemctl start bvdbbkp.service